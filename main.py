# Standard library imports
import ctypes
import hashlib
import json
import os
import pickle
import random
import secrets
import smtplib
import string
import sys
import threading
import time
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv
from config_encryption import encrypt_config_section, decrypt_config_section

# Third-party imports
import firebase_admin
from firebase_admin import db, initialize_app
import keyboard  # For add_hotkey functionality
import pyautogui
import pygetwindow as gw
import winerror
import win32api
import win32event
import wmi

# Tkinter imports
import tkinter as tk
from tkinter import (
    Button, Entry, Label, Listbox, StringVar, Toplevel,
    messagebox, scrolledtext, simpledialog
)

# TTK Bootstrap imports
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

# Pynput imports
from pynput import mouse
from pynput import keyboard as pynput_keyboard
from pynput.keyboard import Controller

# Global log_area variable
log_area = None

# Application mutex name - used to ensure only one instance runs
APP_MUTEX_NAME = "GrandRpHelperSingleInstanceMutex"
app_mutex = None

def check_single_instance():
    """
    Check if another instance of the application is already running.

    Returns:
        bool: True if this is the only instance, False if another instance is already running
    """
    global app_mutex

    try:
        # Try to create a named mutex
        app_mutex = win32event.CreateMutex(None, 1, APP_MUTEX_NAME)
        if win32api.GetLastError() == winerror.ERROR_ALREADY_EXISTS:
            # Another instance is already running
            # Try to find the existing window
            windows = gw.getWindowsWithTitle("Grand Rp Helper")

            if windows:
                # Window is visible, show standard message
                messagebox.showwarning(
                    "Application Already Running",
                    "Grand Rp Helper is already running!\n\n"
                    "The application will now try to bring the existing window to the front."
                )

                try:
                    # Bring the first matching window to front
                    window = windows[0]
                    if window.isMinimized:
                        window.restore()
                    window.activate()

                    # Flash the window to get user's attention
                    ctypes.windll.user32.FlashWindow(ctypes.windll.user32.FindWindowW(None, window.title), True)

                    print("Application is already running. Bringing existing window to front.")
                except Exception as e:
                    print(f"Error bringing existing window to front: {str(e)}")
            else:
                # Window might be hidden, show more detailed message
                messagebox.showwarning(
                    "Application Running in Background",
                    "Grand Rp Helper is already running in the background!\n\n"
                    "The application window might be hidden. Press Alt+H (or your configured hide shortcut) "
                    "to make the window visible again.\n\n"
                    "If you can't restore the window, you may need to restart your computer."
                )

            return False
        return True
    except Exception as e:
        print(f"Error checking for single instance: {str(e)}")
        # In case of error, allow the application to start
        return True
def center_window(window, width=None, height=None):
    """Center a window on the screen

    Args:
        window: The window to center
        width: Optional width to use (if None, uses window's width)
        height: Optional height to use (if None, uses window's height)
    """
    # Get window dimensions if not provided
    if width is None:
        width = window.winfo_width()
        # If window hasn't been updated yet, get requested width
        if width <= 1:
            width = window.winfo_reqwidth()
        # If still no width, get from geometry
        if width <= 1:
            try:
                geometry = window.geometry().split('+')[0]
                width = int(geometry.split('x')[0])
            except:
                width = 400  # Default width

    if height is None:
        height = window.winfo_height()
        # If window hasn't been updated yet, get requested height
        if height <= 1:
            height = window.winfo_reqheight()
        # If still no height, get from geometry
        if height <= 1:
            try:
                geometry = window.geometry().split('+')[0]
                height = int(geometry.split('x')[1])
            except:
                height = 300  # Default height

    # Get screen dimensions
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    # Calculate position
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    # Set window position
    window.geometry(f"{width}x{height}+{x}+{y}")
# Enhanced logging system with buffering and log levels
class Logger:
    """Enhanced logging system with buffering and log levels."""

    # Log levels
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3

    def __init__(self, max_buffer_size=50, default_level=INFO):
        self.buffer = []
        self.max_buffer_size = max_buffer_size
        self.default_level = default_level
        self.current_level = default_level
        self.last_ui_update = 0
        self.update_interval = 0.5  # seconds between UI updates
        self.pending_update = False

    def log(self, message, level=None):
        """Log a message with the specified level."""
        if level is None:
            level = self.default_level

        # Only log if the message level is >= current level
        if level < self.current_level:
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Always print to console for debugging
        level_prefix = {
            self.DEBUG: "[DEBUG]",
            self.INFO: "[INFO]",
            self.WARNING: "[WARNING]",
            self.ERROR: "[ERROR]"
        }.get(level, "[INFO]")

        print(f"{level_prefix} {timestamp} - {message}")

        # Only show specific messages in the UI
        should_display = False

        # Only show email sent confirmations and crash notifications
        if "Email notification sent successfully to:" in message:
            should_display = True
        elif message == "Email notification sent about application crash.":
            should_display = True
        elif level == self.ERROR:  # Always show errors
            should_display = True

        # If message should be displayed, add it to the buffer
        if should_display:
            log_entry = {
                'timestamp': timestamp,
                'message': message,
                'level': level
            }

            # Add to buffer
            self.buffer.append(log_entry)

            # Trim buffer if it exceeds max size
            if len(self.buffer) > self.max_buffer_size:
                self.buffer = self.buffer[-self.max_buffer_size:]

            # Update UI if it's time
            self._update_ui()

    def _update_ui(self):
        """Update the UI log area with buffered messages."""
        try:
            current_time = time.time()

            # Check if we should update the UI
            if (not self.pending_update and
                current_time - self.last_ui_update >= self.update_interval):

                # If log_area exists and is valid
                if log_area and hasattr(log_area, 'winfo_exists') and log_area.winfo_exists():
                    self.pending_update = True

                    # Schedule the actual update to happen in the main thread
                    if 'app' in globals() and hasattr(app, 'after'):
                        app.after(10, self._perform_ui_update)
        except Exception:
            # Silently fail - logging should never crash the application
            pass

    def _perform_ui_update(self):
        """Actually perform the UI update in the main thread."""
        try:
            if log_area and hasattr(log_area, 'winfo_exists') and log_area.winfo_exists():
                # Get all buffered messages
                for entry in self.buffer:
                    timestamp = entry['timestamp']
                    message = entry['message']
                    log_area.insert(tk.END, f"[{timestamp}] {message}\n")

                # Clear buffer after updating
                self.buffer = []

                # Scroll to the end
                log_area.see(tk.END)
                log_area.update_idletasks()

                # Update timestamp
                self.last_ui_update = time.time()
        except Exception as e:
            print(f"[ERROR] Failed to update log UI: {str(e)}")
        finally:
            self.pending_update = False

    def debug(self, message):
        """Log a debug message."""
        self.log(message, self.DEBUG)

    def info(self, message):
        """Log an info message."""
        self.log(message, self.INFO)

    def warning(self, message):
        """Log a warning message."""
        self.log(message, self.WARNING)

    def error(self, message):
        """Log an error message."""
        self.log(message, self.ERROR)

    def set_level(self, level):
        """Set the current logging level."""
        self.current_level = level

# Create global logger instance
logger = Logger()

# Function to log messages to the log area (for backward compatibility)
def log_message(message, level=None):
    """Log a message to the log area (uses the Logger class internally)"""
    if level is None:
        level = logger.INFO
    logger.log(message, level)

class CustomButton(ttk.Button):
    def __init__(self, *args, **kwargs):
        # Extract width if provided, otherwise use default
        width = kwargs.pop('width', 15)

        # Store the original width for later reference
        self._original_width = width

        # Set up consistent styling if not already provided
        if 'style' not in kwargs:
            kwargs['style'] = 'Custom.TButton'

        # Ensure width is always set
        kwargs['width'] = width

        # Remove font if it's in kwargs - ttk.Button doesn't accept font directly
        if 'font' in kwargs:
            kwargs.pop('font')

        super().__init__(*args, **kwargs)

        # Store original style
        self._original_style = kwargs.get('style', 'Custom.TButton')

        # Force width to be consistent
        self.configure(width=width)

        # Bind hover events
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)

        # Bind configure event to maintain width
        self.bind('<Configure>', self._maintain_size)

    def _maintain_size(self, _):
        # Ensure width remains consistent after configuration changes
        current_width = self.cget('width')
        if int(current_width) != int(self._original_width):
            self.configure(width=self._original_width)

    def _on_enter(self, _):
        # Get current style and modify for hover
        current_style = self['style']

        # Don't change hover effect for special button styles
        if current_style in ['RecordStart.TButton', 'RecordStop.TButton',
                            'AutoPressStart.TButton', 'AutoPressStop.TButton',
                            'LoopStart.TButton', 'LoopStop.TButton']:
            return

        if 'success' in self._original_style.lower():
            self['style'] = 'CustomHover.success.TButton'
        elif 'danger' in self._original_style.lower():
            self['style'] = 'CustomHover.danger.TButton'
        else:
            self['style'] = 'CustomHover.TButton'

    def _on_leave(self, _):
        # Restore original style
        self['style'] = self._original_style

class LoadingOverlay:
    def __init__(self, parent):
        self.parent = parent
        self.overlay = None

    def show(self, message="Loading..."):
        # Create overlay window but keep it hidden initially
        self.overlay = ttk.Toplevel(self.parent)
        self.overlay.withdraw()  # Hide initially
        self.overlay.transient(self.parent)
        self.overlay.grab_set()

        # Make it cover the entire parent window
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()

        # If parent window hasn't been updated yet, get dimensions from geometry
        if parent_width <= 1 or parent_height <= 1:
            try:
                geometry = self.parent.geometry().split('+')[0]
                parent_width = int(geometry.split('x')[0])
                parent_height = int(geometry.split('x')[1])
            except:
                parent_width = 1000  # Default width
                parent_height = 600  # Default height

        # Position the overlay to match the parent window
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        self.overlay.geometry(f"{parent_width}x{parent_height}+{parent_x}+{parent_y}")
        self.overlay.overrideredirect(True)

        # Semi-transparent dark background
        self.overlay.configure(background='#000000')
        self.overlay.attributes('-alpha', 0.7)

        # Loading message
        message_label = ttk.Label(
            self.overlay,
            text=message,
            font=('Arial', 14, 'bold'),
            foreground='white',
            background='#000000'
        )
        message_label.place(relx=0.5, rely=0.5, anchor='center')

        # Update the window to ensure it's properly positioned
        self.overlay.update_idletasks()

        # Show the overlay now that it's fully configured
        self.overlay.deiconify()
        self.overlay.lift()
        self.overlay.update()

    def hide(self):
        if self.overlay:
            self.overlay.destroy()
            self.overlay = None

class MessageDialog:
    @staticmethod
    def show_error(title, message):
        # Create dialog window but keep it hidden initially
        dialog = create_centered_window(app, title=title, size="400x250")
        dialog.iconbitmap("Logo.ico")
        dialog.grab_set()

        frame = ttk.Frame(dialog)
        frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Error icon
        ttk.Label(frame,
                text="✖",
                font=("Arial", 48),
                bootstyle="danger").pack(pady=(0,10))

        # Message
        ttk.Label(frame,
                text=message,
                font=("Arial", 12),
                wraplength=350,
                justify="center",
                bootstyle="danger").pack(pady=10)

        # OK button
        ttk.Button(frame,
                  text="OK",
                  command=dialog.destroy,
                  bootstyle="danger-outline",
                  width=15).pack(pady=10)

        dialog.transient(app)

        # Show the dialog now that it's fully configured
        dialog.deiconify()

        dialog.wait_window()

    @staticmethod
    def show_success(title, message):
        # Create dialog window but keep it hidden initially
        dialog = create_centered_window(app, title=title, size="400x300")
        dialog.iconbitmap("Logo.ico")
        dialog.grab_set()

        frame = ttk.Frame(dialog)
        frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Success icon
        ttk.Label(frame,
                text="✓",
                font=("Arial", 48),
                bootstyle="success").pack(pady=(0,10))

        # Message
        ttk.Label(frame,
                text=message,
                font=("Arial", 12),
                wraplength=350,
                justify="center",
                bootstyle="success").pack(pady=10)

        # OK button
        ttk.Button(frame,
                  text="OK",
                  command=dialog.destroy,
                  bootstyle="success-outline",
                  width=15).pack(pady=10)

        dialog.transient(app)

        # Show the dialog now that it's fully configured
        dialog.deiconify()

        dialog.wait_window()

    @staticmethod
    def show_warning(title, message):
        # Create dialog window but keep it hidden initially
        dialog = create_centered_window(app, title=title, size="400x200")
        dialog.iconbitmap("Logo.ico")
        dialog.grab_set()

        frame = ttk.Frame(dialog)
        frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Warning icon
        ttk.Label(frame,
                text="⚠",
                font=("Arial", 48),
                bootstyle="warning").pack(pady=(0,10))

        # Message
        ttk.Label(frame,
                text=message,
                font=("Arial", 12),
                wraplength=350,
                justify="center",
                bootstyle="warning").pack(pady=10)

        # OK button
        ttk.Button(frame,
                  text="OK",
                  command=dialog.destroy,
                  bootstyle="warning-outline",
                  width=15).pack(pady=10)

        dialog.transient(app)

        # Show the dialog now that it's fully configured
        dialog.deiconify()

        dialog.wait_window()

    @staticmethod
    def show_info(title, message):
        # Create dialog window but keep it hidden initially
        dialog = create_centered_window(app, title=title, size="400x220")
        dialog.iconbitmap("Logo.ico")
        dialog.grab_set()

        frame = ttk.Frame(dialog)
        frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Info icon
        ttk.Label(frame,
                text="ℹ",
                font=("Arial", 48),
                bootstyle="info").pack(pady=(0,10))

        # Message
        ttk.Label(frame,
                text=message,
                font=("Arial", 12),
                wraplength=350,
                justify="center",
                bootstyle="info").pack(pady=10)

        # OK button
        ttk.Button(frame,
                  text="OK",
                  command=dialog.destroy,
                  bootstyle="info-outline",
                  width=15).pack(pady=10)

        dialog.transient(app)

        # Show the dialog now that it's fully configured
        dialog.deiconify()

        dialog.wait_window()

    @staticmethod
    def show_input_dialog(title, message, default_value="", input_type="text", validator=None, error_message="Invalid input"):

        result = [None]  # Using list to store result since nonlocal isn't available in static methods

        # Create dialog window but keep it hidden initially
        dialog = create_centered_window(app, title=title, size="400x300")
        dialog.iconbitmap("Logo.ico")
        dialog.grab_set()

        frame = ttk.Frame(dialog)
        frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Input icon
        ttk.Label(frame,
                text="✎",
                font=("Arial", 48),
                bootstyle="primary").pack(pady=(0,10))

        # Message
        ttk.Label(frame,
                text=message,
                font=("Arial", 12),
                wraplength=350,
                justify="center").pack(pady=10)

        # Input field
        input_var = StringVar(value=str(default_value))
        input_entry = ttk.Entry(frame, textvariable=input_var, width=30, bootstyle="primary")
        input_entry.pack(pady=10)

        # Error message label (hidden initially)
        error_label = ttk.Label(frame, text="", bootstyle="danger", font=("Arial", 10))
        error_label.pack(pady=5)

        # Buttons frame
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=10)

        def validate_and_save():
            value = input_var.get()

            # Apply type conversion if needed
            if input_type == "number":
                try:
                    value = float(value)
                except ValueError:
                    error_label.config(text="Please enter a valid number")
                    return

            # Apply custom validation if provided
            if validator and not validator(value):
                error_label.config(text=error_message)
                return

            result[0] = value
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(btn_frame,
                  text="Save",
                  command=validate_and_save,
                  bootstyle="primary-outline",
                  width=10).pack(side=tk.LEFT, padx=5)

        ttk.Button(btn_frame,
                  text="Cancel",
                  command=on_cancel,
                  bootstyle="secondary-outline",
                  width=10).pack(side=tk.LEFT, padx=5)

        # Bind Enter key to save
        input_entry.bind('<Return>', lambda _: validate_and_save())

        dialog.transient(app)

        # Show the dialog now that it's fully configured
        dialog.deiconify()

        # Set focus after the window is visible
        input_entry.focus()

        dialog.wait_window()
        return result[0]

    @staticmethod
    def show_confirm(title, message):
        result = [False]  # Using list to store result since nonlocal isn't available in static methods

        # Create dialog window but keep it hidden initially
        dialog = create_centered_window(app, title=title, size="400x250")
        dialog.iconbitmap("Logo.ico")
        dialog.grab_set()

        frame = ttk.Frame(dialog)
        frame.pack(expand=True, fill='both', padx=20, pady=20)

        # Question icon
        ttk.Label(frame,
                text="?",
                font=("Arial", 48),
                bootstyle="info").pack(pady=(0,10))

        # Message
        ttk.Label(frame,
                text=message,
                font=("Arial", 12),
                wraplength=350,
                justify="center").pack(pady=10)

        # Buttons frame
        btn_frame = ttk.Frame(frame)
        btn_frame.pack(pady=10)

        def on_yes():
            result[0] = True
            dialog.destroy()

        def on_no():
            result[0] = False
            dialog.destroy()

        ttk.Button(btn_frame,
                  text="Yes",
                  command=on_yes,
                  bootstyle="success-outline",
                  width=10).pack(side=tk.LEFT, padx=5)

        ttk.Button(btn_frame,
                  text="No",
                  command=on_no,
                  bootstyle="danger-outline",
                  width=10).pack(side=tk.LEFT, padx=5)

        dialog.transient(app)

        # Show the dialog now that it's fully configured
        dialog.deiconify()

        dialog.wait_window()
        return result[0]

keyboard_controller = Controller()

# Import firebase_config module
from firebase_config import get_firebase_credentials, get_database_url

# Firebase initialization and connection management
class FirebaseManager:
    """Manages Firebase connections with caching and error handling."""

    _instance = None
    _initialized = False
    _admin_initialized = False
    _last_error = None
    _last_connection_time = 0
    _connection_timeout = 30  # seconds before retry after error
    _db_refs = {}  # Cache for database references

    @classmethod
    def get_instance(cls):
        """Singleton pattern to ensure only one manager exists."""
        if cls._instance is None:
            cls._instance = FirebaseManager()
        return cls._instance

    def initialize(self, is_admin=False):
        """Initialize Firebase with improved caching and error handling.

        Args:
            is_admin (bool): Whether to use admin credentials

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        # If already initialized with the right permissions, return immediately
        if self._initialized and (not is_admin or self._admin_initialized):
            return True

        # Check if we should retry after a recent error
        current_time = time.time()
        if (self._last_error is not None and
            current_time - self._last_connection_time < self._connection_timeout):
            log_message(f"Waiting before retrying Firebase connection: {self._last_error}")
            return False

        # Reset error state for new attempt
        self._last_error = None
        self._last_connection_time = current_time

        # Check if Firebase is already initialized
        if not firebase_admin._apps:
            try:
                # Initialize with appropriate credentials
                cred = get_firebase_credentials(is_admin=is_admin)
                initialize_app(cred, {
                    'databaseURL': get_database_url()
                })
                self._initialized = True
                if is_admin:
                    self._admin_initialized = True
                log_message("Firebase connection established")
                return True
            except Exception as e:
                self._last_error = str(e)
                log_message(f"Error initializing Firebase: {self._last_error}")
                return False
        else:
            # Firebase is already initialized
            self._initialized = True
            # If admin access is requested but we don't have it yet,
            # we would need to reinitialize with admin credentials
            # This is a simplification - in a real app, you might need multiple app instances
            if is_admin:
                self._admin_initialized = True
            return True

    def get_db_reference(self, path):
        """Get a database reference with caching.

        Args:
            path (str): The database path

        Returns:
            db.Reference or None: The database reference or None if initialization failed
        """
        if not self.initialize():
            return None

        # Check cache first
        if path in self._db_refs:
            return self._db_refs[path]

        # Create and cache the reference
        try:
            ref = db.reference(path)
            self._db_refs[path] = ref
            return ref
        except Exception as e:
            log_message(f"Error getting database reference for {path}: {str(e)}")
            return None

# Global instance for easy access
firebase_manager = FirebaseManager.get_instance()

def initialize_firebase(is_admin=False):
    """Legacy function for backward compatibility."""
    return firebase_manager.initialize(is_admin)

# Load environment variables from .env file
load_dotenv()

# Email functionality for notifications
class EmailManager:
    """Manages email operations with caching and error handling."""

    _instance = None
    _initialized = False
    _last_error = None
    _last_email_time = 0
    _email_timeout = 60  # seconds between emails to prevent spam

    @classmethod
    def get_instance(cls):
        """Singleton pattern to ensure only one manager exists."""
        if cls._instance is None:
            cls._instance = EmailManager()
        return cls._instance

    def __init__(self):
        self.settings = self.load_email_settings()

    def load_email_settings(self):
        """Load email settings from config.json with encryption."""
        try:
            # Hardcoded SMTP password for security - never stored in config file
            smtp_password = "pulk shfs fabf ycyi"

            # Default settings
            default_settings = {
                "enabled": False,
                "address": "<EMAIL>",
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "smtp_username": "<EMAIL>",
                "smtp_password": smtp_password,
                "use_tls": True,
                "notify_on_crash": True
            }

            # Try to load from config.json
            config_file = "config.json"
            if os.path.exists(config_file):
                try:
                    with open(config_file, "r") as file:
                        config = json.load(file)

                        # Check if email_config section exists and decrypt it
                        if "email_config" in config:
                            email_settings = decrypt_config_section(config, "email_config")
                            if email_settings:
                                # Merge with default settings to ensure all keys exist
                                result = default_settings.copy()
                                result.update(email_settings)

                                # Always use hardcoded password
                                result["smtp_password"] = smtp_password

                                return result
                except (json.JSONDecodeError, ValueError, TypeError) as e:
                    log_message(f"Error parsing config file: {str(e)}")

            # If we get here, return default settings
            return default_settings
        except Exception as e:
            log_message(f"Error loading email settings: {str(e)}")
            # Return default settings if there was an error
            return {
                "enabled": False,
                "address": "<EMAIL>",
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "smtp_username": "<EMAIL>",
                "smtp_password": smtp_password,
                "use_tls": True,
                "notify_on_crash": True
            }

    def save_email_settings(self, settings):
        """Save email settings to config.json with encryption."""
        try:
            # Preserve the hardcoded password
            hardcoded_password = "pulk shfs fabf ycyi"

            # Make a copy of settings to avoid modifying the original
            settings_to_save = settings.copy()

            # Always use the hardcoded password
            settings_to_save["smtp_password"] = hardcoded_password

            # Remove password from what gets saved to file (for extra security)
            settings_to_encrypt = settings_to_save.copy()
            if "smtp_password" in settings_to_encrypt:
                del settings_to_encrypt["smtp_password"]

            # Load existing config
            config = {}
            config_file = "config.json"
            if os.path.exists(config_file):
                try:
                    with open(config_file, "r") as file:
                        config = json.load(file)
                except (json.JSONDecodeError, ValueError, TypeError) as e:
                    log_message(f"Error loading config file: {str(e)}")
                    # Start with empty config if file is corrupted
                    config = {}

            # Encrypt email settings and add to config
            config = encrypt_config_section(config, "email_config", settings_to_encrypt)

            # Save updated config
            with open(config_file, "w") as file:
                json.dump(config, file, indent=2)

            # Update instance settings
            self.settings = settings_to_save

            return True
        except Exception as e:
            log_message(f"Error saving email settings: {str(e)}")
            return False

    def send_email(self, subject, message, force=False):
        """Send an email notification.

        Args:
            subject (str): Email subject
            message (str): Email message body
            force (bool): If True, bypass the timeout check

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        # Check if email notifications are enabled
        if not self.settings.get("enabled", False):
            log_message("Email notifications are disabled")
            return False

        # Check if recipient address is set
        if not self.settings.get("address"):
            log_message("No recipient email address configured")
            return False

        # Check timeout to prevent spam (unless forced)
        current_time = time.time()
        if not force and current_time - self._last_email_time < self._email_timeout:

            return False

        try:
            # Log detailed information for debugging


            # Create message
            msg = MIMEMultipart()
            # Use a professional sender name with the email address
            sender_name = "Grand RP Helper Support"
            sender_email = self.settings.get("smtp_username")
            msg['From'] = f"{sender_name} <{sender_email}>"
            msg['To'] = self.settings.get("address")
            msg['Subject'] = subject

            # Add message body
            msg.attach(MIMEText(message, 'plain'))

            # Connect to SMTP server with timeout and debug level

            server = smtplib.SMTP(
                self.settings.get("smtp_server"),
                self.settings.get("smtp_port"),
                timeout=30  # Add timeout to prevent hanging
            )
            server.set_debuglevel(1)  # Enable debug output

            # Use TLS if enabled
            if self.settings.get("use_tls", True):

                server.starttls()

            # Login if credentials provided
            if self.settings.get("smtp_username") and self.settings.get("smtp_password"):
                server.login(
                    self.settings.get("smtp_username"),
                    self.settings.get("smtp_password")
                )


            # Send email
            log_message("Sending email message...")
            server.send_message(msg)
            server.quit()

            # Update last email time
            self._last_email_time = current_time
            log_message(f"Email notification sent successfully to: {self.settings.get('address')}")
            return True

        except smtplib.SMTPAuthenticationError as e:
            self._last_error = str(e)

            return False
        except smtplib.SMTPConnectError as e:
            self._last_error = str(e)

            return False
        except smtplib.SMTPServerDisconnected as e:
            self._last_error = str(e)

            return False
        except smtplib.SMTPException as e:
            self._last_error = str(e)

            return False
        except Exception as e:
            self._last_error = str(e)

            return False

    def send_crash_notification(self, app_name):
        """Send a notification when an application crashes.

        Args:
            app_name (str): Name of the crashed application

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        # Check if crash notifications are enabled
        if not self.settings.get("notify_on_crash", True):
            return False

        # Create subject and message
        subject = f"Application Monitoring Alert: {app_name} Crash Detected"
        message = f"""
Dear User,

Our monitoring system has detected that the following application has stopped responding:

Application: {app_name}
Time of Detection: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

The system has automatically logged this event. No further action is required from your side.

Best regards,
Grand RP Helper Support Team
        """

        # Send email
        return self.send_email(subject, message)

    def test_email_settings(self):
        """Test email settings by sending a test email.

        Returns:
            bool: True if test email was sent successfully, False otherwise
        """
        subject = "Grand RP Helper - Email Configuration Confirmation"
        message = f"""
Dear User,

This is a confirmation that your email notification system has been successfully configured.

Configuration Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

You will now receive automated notifications when monitored applications stop responding.

Best regards,
Grand RP Helper Support Team
        """

        # Force send regardless of timeout
        return self.send_email(subject, message, force=True)

# Global instance for easy access
email_manager = EmailManager.get_instance()

# License system configuration
LICENSE_FILE = "license.json"

# Files to save parameters and macros
CONFIG_FILE = "config.json"
MACRO_FILE = "macros.json"
APP_FILE = "selected_app.pkl"

# Default parameters for Auto Key Presser
DEFAULT_PARAMS = {
    "click_interval": 1.0,  # Default interval is 1 second
    "key_to_press": "enter",  # Default key to press
    "interaction_key": "d",  # Default key to spam
    "return_delay": 10.0,
    "start_shortcut": "ctrl+shift+s",
    "hide_shortcut": "alt+h",  # Default hide application shortcut
    "license_check_interval": 7 * 24 * 60 * 60,  # 7 days in seconds
}

# Generate unique device ID
def get_device_id():
    try:
        c = wmi.WMI()
        system_info = c.Win32_ComputerSystemProduct()[0]
        cpu_info = c.Win32_Processor()[0]
        bios = c.Win32_BIOS()[0]

        # Combine hardware identifiers
        device_id = f"{system_info.UUID}-{cpu_info.ProcessorId}-{bios.SerialNumber}"
        return hashlib.sha256(device_id.encode()).hexdigest()
    except Exception as e:
        log_message(f"Error generating device ID: {str(e)}")
        return None

# License data storage functions
def save_json_data(data, file_path):
    """Save data to a JSON file"""
    try:
        if not isinstance(data, dict):
            log_message("Invalid data format for saving")
            return False
        with open(file_path, "w") as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        log_message(f"Error saving data: {str(e)}")
        return False

def load_json_data(file_path):
    """Load data from a JSON file"""
    try:
        if not os.path.exists(file_path):
            return None
        with open(file_path, "r") as f:
            return json.load(f)
    except Exception as e:
        log_message(f"Error loading data: {str(e)}")
        return None

class LicenseManager:
    """Manages license verification, caching, and status tracking."""

    # Cache settings
    _cache_duration = 3600  # 1 hour cache for license data

    def __init__(self):
        self.device_id = get_device_id()
        self.license_key = self.load_license_key()
        self.license_data = None
        self.role = None
        self.last_verification_time = 0
        self.verification_result = None

        # If we have a license key, verify it to load the full data
        if self.license_key:
            self.verify_license(self.license_key)

    def load_license_key(self):
        """Load license key from JSON file"""
        data = load_json_data(LICENSE_FILE)
        if data and isinstance(data, dict) and "key" in data:
            return data["key"]
        return None

    def save_license_key(self, license_key):
        """Save only the license key to JSON file"""
        return save_json_data({"key": license_key}, LICENSE_FILE)

    def save_license(self, license_data):
        """Save license data to memory and key to file"""
        self.license_data = license_data
        self.role = license_data.get("role", "user")
        return self.save_license_key(license_data["key"])

    def get_role(self):
        """Get the user role from the license data with caching"""
        # If role is already set, return it
        if self.role:
            return self.role

        # If we have license data, get role from it
        if self.license_data and "role" in self.license_data:
            self.role = self.license_data["role"]
            return self.role

        # If we have a license key but no data, try to get it from Firebase
        if self.license_key:
            try:
                # Get license reference using the manager for caching
                license_ref = firebase_manager.get_db_reference('licenses')
                if license_ref:
                    license_data = license_ref.child(self.license_key).get()
                    if license_data and "role" in license_data:
                        self.role = license_data["role"]
                        return self.role
            except Exception as e:
                # Only log at debug level to reduce noise
                log_message(f"Error getting role: {str(e)}")

        # Default role
        self.role = "user"
        return self.role

    def is_admin(self):
        """Check if the current license has admin privileges"""
        return self.get_role() == "admin"

    def verify_license(self, license_key):
        """Verify license with Firebase and cache the result"""
        current_time = time.time()

        # Use cached verification if available and not expired
        if (self.verification_result and
            license_key == self.license_key and
            current_time - self.last_verification_time < self._cache_duration):
            return self.verification_result

        try:
            # Get license reference using the manager for caching
            license_ref = firebase_manager.get_db_reference('licenses')
            if not license_ref:
                self.verification_result = (False, "Failed to connect to license server")
                return self.verification_result

            # Get license data
            license_data = license_ref.child(license_key).get()
            if not license_data:
                self.verification_result = (False, "License not found")
                return self.verification_result

            if not license_data.get('is_active', False):
                self.verification_result = (False, "License is not active")
                return self.verification_result

            # Check expiry date - handle both old and new license formats
            expiry = license_data.get('expiry_date')

            # For new license format, calculate expiry from activation date and validity days
            if not expiry and license_data.get('activation_date') and license_data.get('validity_days'):
                try:
                    activation_dt = datetime.fromisoformat(license_data['activation_date'])
                    validity_days = license_data['validity_days']
                    expiry_dt = activation_dt + timedelta(days=validity_days)
                    expiry = expiry_dt.isoformat()
                except (ValueError, TypeError):
                    pass  # Fall through to error handling below

            # For unactivated new licenses, expiry check is skipped (they don't expire until activated)
            if expiry:
                if datetime.fromisoformat(expiry) < datetime.now():
                    self.verification_result = (False, "License has expired")
                    return self.verification_result
            elif not license_data.get('validity_days'):
                # Old license format without expiry_date is invalid
                self.verification_result = (False, "Invalid license data")
                return self.verification_result

            # Check license type and usage count for temporary licenses
            license_type = license_data.get('license_type', 'permanent')
            if license_type == 'temporary':
                usage_count = license_data.get('usage_count', 0)
                max_usage = license_data.get('max_usage', 1)

                if usage_count >= max_usage:
                    self.verification_result = (False, "This temporary license has reached its maximum usage limit")
                    return self.verification_result

            # Check device binding
            device_id = license_data.get('device_id')
            current_device = self.device_id

            if device_id and device_id != current_device:
                self.verification_result = (False, "License is bound to another device")
                return self.verification_result

            # Get role from license data
            role = license_data.get('role', 'user')

            # If no device bound, bind it and activate the license
            if not device_id:
                activation_time = datetime.now()
                updates = {
                    'device_id': current_device,
                    'activation_date': activation_time.isoformat(),
                    'is_active': True  # Ensure it's marked as active
                }

                # For new license format, calculate and set expiry_date on activation
                if license_data.get('validity_days') and not license_data.get('expiry_date'):
                    validity_days = license_data['validity_days']
                    expiry_dt = activation_time + timedelta(days=validity_days)
                    updates['expiry_date'] = expiry_dt.isoformat()
                    expiry = expiry_dt.isoformat()  # Update local expiry variable

                # For temporary licenses, increment usage count
                if license_type == 'temporary':
                    updates['usage_count'] = usage_count + 1

                license_ref.child(license_key).update(updates)

            # Save license data locally
            self.save_license({
                "key": license_key,
                "expiry": expiry,
                "device_id": current_device,
                "role": role,
                "license_type": license_type
            })

            # Update role
            self.role = role

            # Update cache
            self.last_verification_time = current_time
            self.verification_result = (True, expiry)
            return self.verification_result

        except Exception as e:
            log_message(f"License verification error: {str(e)}")
            self.verification_result = (False, str(e))
            return self.verification_result

    def check_license_validity(self):
        """Check if the license is valid with caching"""
        # If we don't have a license key, we can't verify
        if not self.license_key:
            return False

        # If we have license data, check if it's bound to this device
        if self.license_data and "device_id" in self.license_data:
            if self.license_data["device_id"] != self.device_id:
                log_message("License is bound to a different device")
                return False

        # Verify with Firebase (uses caching internally)
        is_valid, _ = self.verify_license(self.license_key)
        return is_valid

# Standalone function for backward compatibility
def verify_license(license_key):
    """Verify a license key (uses the LicenseManager internally for caching)"""
    # Create a temporary manager just for this verification if needed
    if not license_manager:
        temp_manager = LicenseManager()
        return temp_manager.verify_license(license_key)

    # Use the global license manager
    return license_manager.verify_license(license_key)

def save_license_data(data):
    """Save license data - only the key is saved to file, rest is kept in memory"""
    try:
        # Only save the key to the file
        if "key" in data:
            if save_json_data({"key": data["key"]}, LICENSE_FILE):
                log_message("License key saved successfully")

                # Update the license manager with the full data
                if license_manager:
                    license_manager.license_data = data
                    license_manager.role = data.get("role", "user")

                update_license_status()
            else:
                raise ValueError("Failed to save license key")
        else:
            raise ValueError("No license key found in data")
    except Exception as e:
        log_message(f"Error saving license: {str(e)}")
        MessageDialog.show_error("Error", "Failed to save license data. Please try again.")

def show_license_dialog():
    """Show standalone license verification window

    Returns:
        bool: True if license is valid, False otherwise
    """
    # Create a standalone window for license verification but keep it hidden initially
    license_window = create_centered_window(
        None,  # No parent for this window
        window_class=ttk.Window,
        title="License Verification",
        size="400x250",
        themename=load_theme()
    )
    license_window.iconbitmap("Logo.ico")
    license_window.resizable(False, False)

    # Prevent the window from being closed with the X button
    # This ensures the user must either verify a license or exit the application
    def on_close():
        # Just ignore the close request
        pass

    license_window.protocol("WM_DELETE_WINDOW", on_close)

    # Show the window now that it's fully configured
    license_window.deiconify()

    # Variable to track verification result
    verification_result = [False]

    ttk.Label(license_window,
             text="Please enter your license key:",
             font=("Arial", 12),
             bootstyle="info").pack(pady=20)

    license_var = StringVar()
    entry = ttk.Entry(license_window, textvariable=license_var, width=40)
    entry.pack(pady=10)
    entry.insert(0, "")  # Clear any default value
    entry.focus()  # Auto-focus the entry field

    message_label = ttk.Label(license_window,
                            text="Enter your license key to activate the application",
                            font=("Arial", 10),
                            wraplength=350)
    message_label.pack(pady=10)

    def verify():
        try:
            key = license_var.get().strip()
            if not key:
                message_label.config(text="Please enter a license key", bootstyle="danger")
                return

            message_label.config(text="Verifying license...", bootstyle="info")
            license_window.update()

            # Initialize Firebase if not already initialized
            if not initialize_firebase():
                message_label.config(text="Failed to connect to license server", bootstyle="danger")
                return

            # Get license from Firebase
            ref = db.reference('licenses')
            license_data = ref.child(key).get()

            if not license_data:
                message_label.config(text="Invalid license key", bootstyle="danger")
                return

            if not license_data.get('is_active', False):
                message_label.config(text="License is not active", bootstyle="danger")
                return

            # Check expiry date - handle both old and new license formats
            expiry = license_data.get('expiry_date')

            # For new license format, calculate expiry from activation date and validity days
            if not expiry and license_data.get('activation_date') and license_data.get('validity_days'):
                try:
                    activation_dt = datetime.fromisoformat(license_data['activation_date'])
                    validity_days = license_data['validity_days']
                    expiry_dt = activation_dt + timedelta(days=validity_days)
                    expiry = expiry_dt.isoformat()
                except (ValueError, TypeError):
                    pass  # Fall through to error handling below

            # For unactivated new licenses, expiry check is skipped (they don't expire until activated)
            if expiry:
                if datetime.fromisoformat(expiry) < datetime.now():
                    message_label.config(text="License has expired", bootstyle="danger")
                    return
            elif not license_data.get('validity_days'):
                # Old license format without expiry_date is invalid
                message_label.config(text="Invalid license data", bootstyle="danger")
                return

            # Check license type and usage count for temporary licenses
            license_type = license_data.get('license_type', 'permanent')
            if license_type == 'temporary':
                usage_count = license_data.get('usage_count', 0)
                max_usage = license_data.get('max_usage', 1)

                if usage_count >= max_usage:
                    message_label.config(text="This temporary license has reached its maximum usage limit", bootstyle="danger")
                    return

            device_id = license_data.get('device_id')
            current_device = get_device_id()

            if device_id and device_id != current_device:
                message_label.config(text="License is bound to another device", bootstyle="danger")
                return

            # License is valid - update Firebase if needed
            updates = {}
            if not device_id:
                activation_time = datetime.now()
                updates['device_id'] = current_device
                updates['activation_date'] = activation_time.isoformat()

                # For new license format, calculate and set expiry_date on activation
                if license_data.get('validity_days') and not license_data.get('expiry_date'):
                    validity_days = license_data['validity_days']
                    expiry_dt = activation_time + timedelta(days=validity_days)
                    updates['expiry_date'] = expiry_dt.isoformat()
                    expiry = expiry_dt.isoformat()  # Update local expiry variable

                # For temporary licenses, increment usage count
                if license_type == 'temporary':
                    updates['usage_count'] = license_data.get('usage_count', 0) + 1

                ref.child(key).update(updates)

            try:
                # Save license data locally
                role = license_data.get('role', 'user')
                license_type = license_data.get('license_type', 'permanent')
                license_info = {
                    "key": key,
                    "expiry": expiry,
                    "device_id": current_device,
                    "role": role,
                    "license_type": license_type
                }

                # Save license data - only key is saved to file
                if save_json_data({"key": license_info["key"]}, LICENSE_FILE):
                    # Set verification result to True
                    verification_result[0] = True

                    # Update the license manager with the full data
                    if license_manager:
                        license_manager.license_data = license_info
                        license_manager.role = license_info.get("role", "user")

                    # Show success message before closing
                    message_label.config(text="License verified successfully!", bootstyle="success")
                    license_window.update()

                    # Wait a moment to show the success message and then restart the application
                    def restart_app():
                        license_window.destroy()
                        # Start a new process for the main application
                        import subprocess
                        import sys
                        subprocess.Popen([sys.executable, sys.argv[0]])
                        # Exit this process
                        sys.exit()

                    license_window.after(1500, restart_app)
                else:
                    message_label.config(text="Error saving license data", bootstyle="danger")
            except Exception as e:
                message_label.config(text=f"Error saving license: {str(e)}", bootstyle="danger")

        except Exception as e:
            log_message(f"License verification error: {str(e)}")
            message_label.config(text=f"Verification error: {str(e)}", bootstyle="danger")

    def on_enter(_):
        verify()

    entry.bind('<Return>', on_enter)  # Allow verification with Enter key

    # Create a button frame for multiple buttons
    button_frame = ttk.Frame(license_window)
    button_frame.pack(pady=10)

    # Verify license button
    ttk.Button(button_frame,
              text="Verify License",
              command=verify,
              bootstyle="primary",
              width=15).pack(side=tk.LEFT, padx=5)

    # Exit button
    ttk.Button(button_frame,
              text="Exit",
              command=lambda: sys.exit(),
              bootstyle="danger",
              width=15).pack(side=tk.LEFT, padx=5)

    # Run this window's main loop
    license_window.mainloop()

    # Return verification result
    return verification_result[0]

# License status management is now handled by LicenseStatusManager class

class LicenseStatusManager:
    """Manages license status display with optimized UI updates."""

    # Update intervals
    CHECK_INTERVAL = 60  # Check license validity every 60 seconds
    UI_UPDATE_INTERVAL = 10  # Update UI every 10 seconds

    def __init__(self):
        # Status tracking
        self.last_check_time = 0
        self.last_ui_update_time = 0
        self.expiry_date = None
        self.is_admin = False
        self.last_status_text = ""
        self.last_style = ""
        self.admin_button_visible = False
        self.update_scheduled = False

    def update_status(self):
        """Update license status with optimized checking and UI updates."""
        try:
            # Skip if app doesn't exist
            if not hasattr(app, 'winfo_exists') or not app.winfo_exists():
                return

            current_time = time.time()
            recalculate = False

            # Check if we need to recalculate license data (less frequently)
            if current_time - self.last_check_time > self.CHECK_INTERVAL:
                recalculate = True
                self.last_check_time = current_time

                # Check license validity
                if license_manager.check_license_validity():
                    try:
                        self.expiry_date = datetime.fromisoformat(license_manager.license_data["expiry"])
                        self.is_admin = license_manager.is_admin()
                    except Exception as e:
                        logger.error(f"Error getting license data: {str(e)}")
                        self.expiry_date = None
                else:
                    self.expiry_date = None

            # Check if we should update the UI
            if recalculate or current_time - self.last_ui_update_time > self.UI_UPDATE_INTERVAL:
                self.last_ui_update_time = current_time

                # Calculate status text and style
                status_text, style = self._calculate_status_text()

                # Only update UI if text or style has changed
                if status_text != self.last_status_text or style != self.last_style:
                    if hasattr(license_status_label, 'winfo_exists') and license_status_label.winfo_exists():
                        license_status_label.config(text=status_text, bootstyle=style)
                        self.last_status_text = status_text
                        self.last_style = style

                # Update admin button visibility if needed
                if recalculate:
                    self._update_admin_button_visibility()

            # Schedule next update
            self.update_scheduled = False
            self._schedule_next_update()

        except Exception as e:
            logger.error(f"Error updating license status: {str(e)}")
            # Ensure we reschedule even after error
            self.update_scheduled = False
            self._schedule_next_update()

    def _calculate_status_text(self):
        """Calculate the status text and style based on license data."""
        if self.expiry_date is not None:
            try:
                # Get current time
                now = datetime.now()
                time_delta = self.expiry_date - now

                # Calculate time components
                total_seconds = time_delta.total_seconds()
                days = int(total_seconds // (24 * 3600))
                remaining_seconds = total_seconds % (24 * 3600)
                hours = int(remaining_seconds // 3600)

                role_text = " (Admin)" if self.is_admin else ""

                # Format based on time remaining
                if days > 7:
                    return f"Licensed{role_text} - {days}d {hours}h", "success"
                else:
                    return f"License{role_text} expires in {days}d {hours}h", "warning"
            except Exception:
                return "License valid", "success"
        else:
            return "License Required", "danger"

    def _update_admin_button_visibility(self):
        """Update admin button visibility based on admin status."""
        if hasattr(admin_button, 'winfo_exists') and admin_button.winfo_exists():
            should_show = self.is_admin

            # Only update if visibility state needs to change
            if should_show != self.admin_button_visible:
                if should_show:
                    admin_button.pack(side=tk.RIGHT, padx=5, pady=5)
                else:
                    admin_button.pack_forget()
                self.admin_button_visible = should_show

    def _schedule_next_update(self):
        """Schedule the next status update."""
        if not self.update_scheduled and hasattr(app, 'after'):
            # Use different intervals based on license validity
            if license_manager and license_manager.check_license_validity():
                interval = 5000  # 5 seconds if license is valid
            else:
                interval = 10000  # 10 seconds if no valid license

            app.after(interval, self.update_status)
            self.update_scheduled = True

    def force_update(self):
        """Force an immediate status update."""
        self.last_check_time = 0  # Reset last check time to force recalculation
        self.update_status()

# Create global license status manager
license_status_manager = LicenseStatusManager()

def update_license_status():
    """Legacy function for backward compatibility."""
    license_status_manager.update_status()

# Cache for loaded parameters to avoid file I/O
_cached_parameters = None
_parameters_last_modified = 0
_params_check_time = 0
_params_check_interval = 5  # Check file modification time every 5 seconds

def load_parameters():
    """Load parameters with caching to reduce file I/O"""
    global _cached_parameters, _parameters_last_modified, _params_check_time

    try:
        current_time = time.time()

        # If we have cached parameters and it's not time to check for changes yet, return cached version
        if _cached_parameters is not None and (current_time - _params_check_time) < _params_check_interval:
            return _cached_parameters.copy()  # Return a copy to prevent modification of cache

        _params_check_time = current_time

        # Check if file exists and get its modification time
        try:
            current_mtime = os.path.getmtime(CONFIG_FILE)
        except OSError:
            # File doesn't exist, return defaults and don't update cache time
            if _cached_parameters is None:
                _cached_parameters = DEFAULT_PARAMS.copy()
            return _cached_parameters.copy()

        # If file hasn't changed since last load, return cached version
        if _cached_parameters is not None and current_mtime <= _parameters_last_modified:
            return _cached_parameters.copy()

        # Load from file if cache is invalid or file has changed
        with open(CONFIG_FILE, "r") as file:
            loaded_params = json.load(file)
            # Create a new dict with defaults, updated with loaded values
            result = DEFAULT_PARAMS.copy()
            result.update(loaded_params)

            # Update cache
            _cached_parameters = result.copy()
            _parameters_last_modified = current_mtime

            return result

    except (json.JSONDecodeError, ValueError, TypeError):
        # If file exists but is invalid, use defaults or existing cache
        if _cached_parameters is None:
            _cached_parameters = DEFAULT_PARAMS.copy()
        return _cached_parameters.copy()


saved_macros_popup = None
loop_start_time = 0
sleep_end_time = None  # Tracks when the current sleep phase ends
timer_lock = threading.Lock()
def create_centered_window(parent, window_class=ttk.Toplevel, title="Window", size="400x300", **kwargs):
    """Create a window centered on screen

    Args:
        parent: Parent window
        window_class: Window class to use (ttk.Toplevel or ttk.Window)
        title: Window title
        size: Window size in format "widthxheight"
        **kwargs: Additional arguments to pass to the window constructor

    Returns:
        The created window (hidden)
    """
    # Create window but keep it hidden initially
    window = window_class(parent, **kwargs)
    window.withdraw()  # Hide window initially
    window.title(title)

    # Extract width and height from size string
    try:
        width, height = map(int, size.split('x'))
    except:
        width, height = 400, 300  # Default dimensions

    # Get screen dimensions
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    # Calculate position
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    # Set window position and size
    window.geometry(f"{width}x{height}+{x}+{y}")

    # Return the window (still hidden)
    return window

def create_popup(title="Window", size="400x300"):
    """Create a popup window centered on screen

    Args:
        title: Window title
        size: Window size in format "widthxheight"

    Returns:
        The created popup window
    """
    # Create window but keep it hidden initially
    popup = create_centered_window(
        app,
        title=title,
        size=size
    )

    # Configure popup-specific settings
    popup.configure(bg=get_theme_background_color(current_theme))
    popup.iconbitmap("Logo.ico")
    popup.attributes("-topmost", True)

    # Show the window now that it's fully configured
    popup.deiconify()

    return popup


# Function to display the list of saved macros
def view_macros():
    """Display a list of saved macros in a popup window.

    This function creates a popup window that displays all saved macros
    and provides options to modify or delete them.
    """
    global saved_macros_popup

    # Check if there are any macros to display
    if not macros:
        log_message("No macros saved.")
        return

    # If the popup already exists, bring it to the front and return
    if saved_macros_popup is not None and saved_macros_popup.winfo_exists():
        saved_macros_popup.lift()  # Bring the window to the front
        saved_macros_popup.focus_force()  # Focus on the window
        return

    # Create a new popup window to display the list of macros
    saved_macros_popup = create_centered_window(
        app,
        window_class=Toplevel,
        title="Saved Macros",
        size="400x300"
    )
    saved_macros_popup.configure(bg="#2E3440")
    saved_macros_popup.iconbitmap("Logo.ico")

    # Show the popup now that it's fully configured
    saved_macros_popup.deiconify()

    # Function to handle popup close event
    def on_saved_macros_popup_close():
        global saved_macros_popup
        saved_macros_popup.destroy()
        saved_macros_popup = None  # Reset the global variable

    # Ensure the popup is closed properly when the user clicks the close button
    saved_macros_popup.protocol("WM_DELETE_WINDOW", on_saved_macros_popup_close)

    # Create and pack the header label
    label = Label(saved_macros_popup, text="Saved Macros:")
    label.pack(pady=10)

    # Create and pack the listbox to display macros
    listbox = Listbox(
        saved_macros_popup,
        bg="#23272a",
        fg="white",
        font=("Consolas", 10)
    )
    listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Populate the listbox with saved macros
    for macro_name in macros.keys():
        listbox.insert(tk.END, macro_name)

    # Function to delete the selected macro
    def delete_macro():
        selected_index = listbox.curselection()
        if not selected_index:
            log_message("No macro selected.")
            return

        # Get the selected macro name
        macro_name = listbox.get(selected_index[0])

        # Confirm deletion with the user
        confirm = MessageDialog.show_confirm(
            "Delete Macro",
            f"Are you sure you want to delete the macro '{macro_name}'?"
        )
        if not confirm:
            return

        # Delete the macro from the dictionary
        del macros[macro_name]
        save_macros(macros)  # Save the updated macros to the file
        log_message(f"Macro '{macro_name}' deleted.")

        # Refresh the listbox
        listbox.delete(selected_index)

    # Create button frame for better layout
    button_frame = ttk.Frame(saved_macros_popup)
    button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

    # Button to modify the selected macro
    modify_button = Button(
        button_frame,
        text="Modify Macro",
        command=lambda: modify_macro(listbox.get(tk.ACTIVE)),
    )
    modify_button.pack(side=tk.LEFT, padx=5)

    # Button to delete the selected macro
    delete_button = Button(
        button_frame,
        text="Delete Macro",
        command=delete_macro
    )
    delete_button.pack(side=tk.LEFT, padx=5)


# Global variable to track the modify macro popup window
modify_macro_popup = None


def get_repeat_count(macro_name):
    # Create popup window but keep it hidden initially
    popup = create_centered_window(
        app,
        window_class=Toplevel,
        title="Repeat Macro",
        size="300x150"
    )
    popup.attributes("-topmost", True)
    popup.iconbitmap("Logo.ico")  # Added logo icon

    # Store the result in a variable that persists after window destruction
    result = {"value": None}

    Label(popup, text=f"Repeat '{macro_name}' how many times?").pack(pady=10)

    entry = Entry(popup)
    entry.pack(pady=5)
    entry.insert(0, "1")

    def on_confirm():
        # Get the value before destroying the window
        try:
            result["value"] = int(entry.get())
        except ValueError:
            result["value"] = 1
        popup.destroy()

    Button(popup, text="OK", command=on_confirm).pack(pady=5)

    # Make sure we handle window closure properly
    popup.protocol("WM_DELETE_WINDOW", lambda: popup.destroy())

    # Show the popup now that it's fully configured
    popup.deiconify()

    popup.grab_set()
    popup.wait_window()

    # Return 1 if user closed window without confirmation
    return result["value"] or 1

def record_single_step(macro_name, insert_index):
    global recording, recorded_steps, last_timestamp, refresh_steps

    recorded_steps = []
    last_timestamp = None
    recording = True

    start_recording()
    log_message("Recording started. Press ESC to stop and insert steps.")

    def wait_for_esc():
        keyboard.wait("esc")
        stop_recording()
        if recorded_steps:
            for i, step in enumerate(recorded_steps):
                macros[macro_name].insert(insert_index + i, step)
            save_macros(macros)
            log_message("Step(s) inserted.")
        else:
            log_message("No step recorded.")

        try:
            refresh_steps()
        except:
            pass

    threading.Thread(target=wait_for_esc, daemon=True).start()


def modify_macro(macro_name):
    global modify_macro_popup

    print("modify_macro function called")  # Debug message

    if macro_name not in macros:
        log_message(f"Macro '{macro_name}' not found.")
        return

    if modify_macro_popup is not None and modify_macro_popup.winfo_exists():
        modify_macro_popup.lift()
        modify_macro_popup.focus_force()
        return

    # Create window but keep it hidden initially
    modify_macro_popup = create_centered_window(
        app,
        window_class=Toplevel,
        title=f"Modify Macro: {macro_name}",
        size="600x500"
    )
    modify_macro_popup.iconbitmap("Logo.ico")  # Added logo icon
    modify_macro_popup.protocol("WM_DELETE_WINDOW", lambda: on_modify_popup_close())

    Label(modify_macro_popup, text=f"Steps in Macro: {macro_name}").pack(pady=10)

    steps_listbox = Listbox(
    modify_macro_popup,
    bg="#23272a",
    fg="white",
    font=("Consolas", 10),
    selectmode=tk.EXTENDED

)
    ttk.Button(modify_macro_popup, text="Select All", command=lambda: steps_listbox.select_set(0, tk.END)).pack(pady=5, side=tk.TOP)
    ttk.Button(modify_macro_popup, text="Deselect All", command=lambda: steps_listbox.select_clear(0, tk.END)).pack(pady=5, side=tk.TOP)


    steps_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    def on_start_drag(event):
        # Get the item clicked
        widget = event.widget
        index = widget.nearest(event.y)
        if index >= 0:
            drag_data["index"] = index

    def on_drag_motion(_):
        pass  # Optional: visual feedback while dragging (e.g. highlight)

    def on_drop(event):
        widget = event.widget
        new_index = widget.nearest(event.y)
        old_index = drag_data["index"]

        if old_index is None or new_index == old_index:
            return

        # Move the step in the macro list
        step = macros[macro_name].pop(old_index)
        macros[macro_name].insert(new_index, step)
        save_macros(macros)

        # Refresh and reselect new position
        refresh_steps()
        steps_listbox.selection_set(new_index)
        drag_data["index"] = None
    # Events for drag and drop
    steps_listbox.bind("<ButtonPress-1>", on_start_drag)
    steps_listbox.bind("<B1-Motion>", on_drag_motion)
    steps_listbox.bind("<ButtonRelease-1>", on_drop)

    steps_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    modify_macro(macro_name)


    def move_step_up():
        idx = steps_listbox.curselection()
        if not idx or idx[0] == 0:
            return
        i = idx[0]
        macros[macro_name][i - 1], macros[macro_name][i] = macros[macro_name][i], macros[macro_name][i - 1]
        save_macros(macros)
        refresh_steps()
        steps_listbox.selection_set(i - 1)

    def move_step_down():
        idx = steps_listbox.curselection()
        if not idx or idx[0] == len(macros[macro_name]) - 1:
            return
        i = idx[0]
        macros[macro_name][i + 1], macros[macro_name][i] = macros[macro_name][i], macros[macro_name][i + 1]
        save_macros(macros)
        refresh_steps()
        steps_listbox.selection_set(i + 1)
    ttk.Button(modify_macro_popup, text="⬆ Move Up", command=move_step_up).pack(pady=5)
    ttk.Button(modify_macro_popup, text="⬇ Move Down", command=move_step_down).pack(pady=5)


    drag_data = {"index": None}


    def refresh_steps():
        steps_listbox.delete(0, tk.END)
        for step in macros[macro_name]:
            steps_listbox.insert(
                tk.END,
                f"{step['type']}: {step['details']} (Delay: {step['delay']}, Duration: {step['duration']})",
            )

    refresh_steps()






    def delete_step():
        selected_indices = list(steps_listbox.curselection())
        if not selected_indices:
            return



        # حذف من الأعلى للأسفل لتجنب اختلال الفهارس
        for index in reversed(selected_indices):
            macros[macro_name].pop(index)

        save_macros(macros)
        refresh_steps()


    def open_step_popup(title, step_data=None, is_edit=False, index=None):
        # Create step popup window but keep it hidden initially
        step_popup = create_centered_window(
            modify_macro_popup,
            window_class=Toplevel,
            title=title,
            size="400x300"
        )
        step_popup.iconbitmap("Logo.ico")  # Added logo icon

        # Show the popup now that it's fully configured
        step_popup.deiconify()

        Label(step_popup, text=title).pack(pady=10)

        type_var = StringVar(value=step_data["type"] if step_data else "mouse_click")
        details_var = StringVar(
            value=(
                str(step_data["details"])
                if step_data
                else "{'x': 100, 'y': 100, 'button': 'left'}"
            )
        )
        delay_var = StringVar(value=str(step_data["delay"]) if step_data else "0.0")
        duration_var = StringVar(
            value=str(step_data["duration"]) if step_data else "1.0"
        )

        def create_entry(label_text, var):
            Label(step_popup, text=label_text).pack()
            entry = Entry(step_popup, textvariable=var)
            entry.pack()
            return entry

        create_entry("Type:", type_var)
        create_entry("Details:", details_var)
        create_entry("Delay:", delay_var)
        create_entry("Duration:", duration_var)

        def save_step():
            new_step = {
                "type": type_var.get(),
                "details": eval(details_var.get()),
                "delay": float(delay_var.get()),
                "duration": float(duration_var.get()),
            }
            if is_edit and index is not None:
                macros[macro_name][index] = new_step
                log_message(f"Step updated in macro '{macro_name}'.")
            else:
                macros[macro_name].append(new_step)
                log_message(f"New step added to macro '{macro_name}'.")
            save_macros(macros)
            step_popup.destroy()
            refresh_steps()

        Button(step_popup, text="Save", command=save_step).pack(pady=10)

    def add_step():
        # Check if the macro has any steps
        if len(macros[macro_name]) == 0:
            # If macro is empty, automatically set insert_index to 0
            insert_index = 0
        else:
            # If macro has steps, check for selected position
            idx = steps_listbox.curselection()
            if not idx:
                MessageDialog.show_info("Select Position", "Please select where to insert the new step.")
                return
            insert_index = idx[0]

        response = MessageDialog.show_confirm("Start Recording", "Start recording now?\n(Press ESC to stop when done)")
        if response:
            record_single_step(macro_name, insert_index)


    def edit_step():
        selected_index = steps_listbox.curselection()
        if not selected_index:
            log_message("No step selected.")
            return
        open_step_popup(
            "Edit Step",
            macros[macro_name][selected_index[0]],
            is_edit=True,
            index=selected_index[0],
        )

    Button(modify_macro_popup, text="Edit Step", command=edit_step).pack(
        side=tk.LEFT, padx=5, pady=10
    )
    Button(modify_macro_popup, text="Delete Step", command=delete_step).pack(
        side=tk.LEFT, padx=5, pady=10
    )
    Button(modify_macro_popup, text="Add Step", command=add_step).pack(
        side=tk.LEFT, padx=5, pady=10
    )

    def on_modify_popup_close():
        global modify_macro_popup
        modify_macro_popup.destroy()
        modify_macro_popup = None

    # Show the window now that it's fully configured
    modify_macro_popup.deiconify()


# Save parameters to file with throttling to reduce disk I/O
_last_save_time = 0
_save_throttle_interval = 1.0  # Minimum seconds between saves
_pending_save = False
_params_to_save = None

def save_parameters(params):
    """Save parameters with throttling to reduce disk I/O"""
    global _last_save_time, _pending_save, _params_to_save, _cached_parameters

    # Update the cache immediately
    global _parameters_last_modified
    if _cached_parameters is not None:
        _cached_parameters = params.copy()
        _parameters_last_modified = time.time()

    # Store the params to save
    _params_to_save = params.copy()

    # Check if we need to throttle the save
    current_time = time.time()
    if current_time - _last_save_time < _save_throttle_interval:
        # If a save is already pending, just update the params
        if not _pending_save:
            _pending_save = True
            # Schedule a save after the throttle interval
            if hasattr(app, 'after'):
                app.after(int(_save_throttle_interval * 1000), _do_save_parameters)
        return

    # If we're here, we can save immediately
    _do_save_parameters()

def _do_save_parameters():
    """Actually perform the save operation"""
    global _last_save_time, _pending_save, _params_to_save

    if _params_to_save is None:
        _pending_save = False
        return

    try:
        with open(CONFIG_FILE, "w") as file:
            json.dump(_params_to_save, file, indent=4)
        _last_save_time = time.time()
    except Exception as e:
        log_message(f"Error saving parameters: {str(e)}")

    _pending_save = False


# Cache for loaded macros to avoid file I/O
_cached_macros = None
_macros_last_modified = 0
_macros_check_time = 0
_macros_check_interval = 5  # Check file modification time every 5 seconds

# Load macros from file with caching
def load_macros():
    """Load macros with caching to reduce file I/O"""
    global _cached_macros, _macros_last_modified, _macros_check_time

    try:
        current_time = time.time()

        # If we have cached macros and it's not time to check for changes yet, return cached version
        if _cached_macros is not None and (current_time - _macros_check_time) < _macros_check_interval:
            return _cached_macros.copy()  # Return a copy to prevent modification of cache

        _macros_check_time = current_time

        # Check if file exists and get its modification time
        try:
            current_mtime = os.path.getmtime(MACRO_FILE)
        except OSError:
            # File doesn't exist, return empty dict and don't update cache time
            if _cached_macros is None:
                _cached_macros = {}
            return _cached_macros.copy()

        # If file hasn't changed since last load, return cached version
        if _cached_macros is not None and current_mtime <= _macros_last_modified:
            return _cached_macros.copy()

        # Load from file if cache is invalid or file has changed
        with open(MACRO_FILE, "r") as file:
            loaded_macros = json.load(file)

            # Update cache
            _cached_macros = loaded_macros.copy()
            _macros_last_modified = current_mtime

            return loaded_macros

    except (FileNotFoundError, json.JSONDecodeError, ValueError, TypeError):
        # If file exists but is invalid, use empty dict or existing cache
        if _cached_macros is None:
            _cached_macros = {}
        return _cached_macros.copy()


macros = load_macros()  # Ensure `macros` is initialized


# Throttled macro saving to reduce disk I/O
_last_macro_save_time = 0
_macro_save_throttle_interval = 1.0  # Minimum seconds between saves
_pending_macro_save = False
_macros_to_save = None

def save_macros(macros):
    """Save macros with throttling to reduce disk I/O"""
    global _last_macro_save_time, _pending_macro_save, _macros_to_save

    # Store the macros to save
    _macros_to_save = macros.copy()

    # Check if we need to throttle the save
    current_time = time.time()
    if current_time - _last_macro_save_time < _macro_save_throttle_interval:
        # If a save is already pending, just update the macros
        if not _pending_macro_save:
            _pending_macro_save = True
            # Schedule a save after the throttle interval
            if hasattr(app, 'after'):
                app.after(int(_macro_save_throttle_interval * 1000), _do_save_macros)
        return

    # If we're here, we can save immediately
    _do_save_macros()

def _do_save_macros():
    """Actually perform the macro save operation"""
    global _last_macro_save_time, _pending_macro_save, _macros_to_save

    if _macros_to_save is None:
        _pending_macro_save = False
        return

    try:
        with open("macros.json", "w") as file:
            json.dump(_macros_to_save, file, indent=4)
        _last_macro_save_time = time.time()
        # Only log occasionally to reduce overhead
        if random.random() < 0.1:  # 10% chance to log
            log_message("Macros saved successfully.")
    except Exception as e:
        log_message(f"Error saving macros: {e}")

    _pending_macro_save = False


# Global variables for Auto Key Presser
params = load_parameters()
auto_press_running = False
loop_running = False
selected_app = None

# Global variables for Macro Recorder

recording = False
recorded_steps = []
listener_active = False
press_times = {}
mouse_listener = None
keyboard_listener = None
lock = threading.Lock()

themes = [
    "darkly",
    "superhero",
    "cyborg",
    "solar",
    "journal",
    "flatly",
    "morph",
    "minty",
    "pulse",
    "united",
    "yeti",
    "sandstone",
    "cosmo",
    "litera",
    "lumen",
    "simplex",
    "cerculean",
]


# Cache for theme to avoid file I/O
_cached_theme = None

def load_theme():
    """Load the saved theme from config.json with caching."""
    global _cached_theme

    # Return cached theme if available
    if _cached_theme is not None:
        return _cached_theme

    # Load from config.json
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, "r") as file:
                config = json.load(file)
                if "theme" in config:
                    _cached_theme = config["theme"]
                    return _cached_theme
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            log_message(f"Error loading theme from config: {str(e)}")

    # Default theme if not found
    _cached_theme = "darkly"
    return _cached_theme


def save_theme(theme):
    """Save the selected theme to config.json."""
    global _cached_theme

    # Update cache
    _cached_theme = theme

    # Load existing config
    config = {}
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, "r") as file:
                config = json.load(file)
        except (json.JSONDecodeError, ValueError, TypeError) as e:
            log_message(f"Error loading config file: {str(e)}")
            # Start with empty config if file is corrupted
            config = {}

    # Update theme
    config["theme"] = theme

    # Save updated config
    with open(CONFIG_FILE, "w") as file:
        json.dump(config, file, indent=2)


def get_theme_background_color(theme_name=None):
    """Get the appropriate background color for the current theme."""
    if theme_name is None:
        theme_name = load_theme()





def apply_custom_styles():
    """Apply consistent styles to prevent theme-related size changes."""
    app.option_add("*TLabel.Font", "Arial 12")  # Fix label font size
    app.option_add("*TButton.Font", "Arial 10")  # Fix button font size
    app.option_add("*TCombobox.Font", "Arial 12")  # Fix dropdown font size
    app.option_add("*TFrame.Padding", "10 10")  # Fix padding for frames

    # Re-apply button styles to ensure consistency
    setup_custom_styles()


def change_theme(_=None):
    """Update the app theme and save it."""
    # _ parameter is used by the event binding system
    # Using underscore to indicate intentionally unused parameter
    selected_theme = theme_combobox.get()
    app.style.theme_use(selected_theme)
    save_theme(selected_theme)
    theme_label.config(text=f"Current Theme: {selected_theme}")

    # Apply custom styles to ensure consistent button sizes
    apply_custom_styles()

    # Force update all buttons to maintain consistent appearance
    for widget in app.winfo_children():
        _update_widget_styles(widget)

    # Explicitly refresh button styles to ensure correct colors
    refresh_button_styles()

def _update_widget_styles(widget):
    """Recursively update styles for all widgets"""
    if isinstance(widget, ttk.Button):
        if isinstance(widget, CustomButton):
            # For CustomButton, make sure the style is applied correctly
            current_style = widget['style']
            if current_style == 'AutoPressStart.TButton':
                widget.configure(style='AutoPressStart.TButton')
            elif current_style == 'AutoPressStop.TButton':
                widget.configure(style='AutoPressStop.TButton')
            elif current_style == 'LoopStart.TButton':
                widget.configure(style='LoopStart.TButton')
            elif current_style == 'LoopStop.TButton':
                widget.configure(style='LoopStop.TButton')
            elif current_style == 'RecordStart.TButton':
                widget.configure(style='RecordStart.TButton')
            elif current_style == 'RecordStop.TButton':
                widget.configure(style='RecordStop.TButton')
        else:
            # Force consistent styling on regular ttk buttons
            widget.configure(font=('Arial', 10), width=15)

    # Process all children widgets
    for child in widget.winfo_children():
        _update_widget_styles(child)

def change_hide_shortcut():
    """Change the shortcut for hiding/showing the application"""
    global params

    new_shortcut = record_shortcut(app, "Record Hide Application Shortcut")
    if new_shortcut and validate_shortcut(new_shortcut):
        # Safely remove old shortcut if exists
        try:
            keyboard.remove_hotkey(params["hide_shortcut"])
        except KeyError:
            pass

        # Save and update
        params["hide_shortcut"] = new_shortcut.lower()
        save_parameters(params)

        # Add the new shortcut
        keyboard.add_hotkey(params["hide_shortcut"], toggle_app_visibility)

        # Update button text
        hide_shortcut_btn.config(text=f"Hide the application: {new_shortcut.upper()}")
        log_message(f"Hide from taskbar shortcut updated to '{new_shortcut}'")


# Function to initialize and create the main application window
def create_main_application():
    global app, loading_overlay, license_manager, log_area, admin_button, current_theme , center_window

    # Load the saved theme
    current_theme = load_theme()

    # Get the appropriate background color for the theme
    bg_color = get_theme_background_color(current_theme)

    # Create the main app window
    app = ttk.Window(themename=current_theme)
    app.withdraw()  # Hide initially
    app.title("Grand Rp Helper")
    app.resizable(False, False)
    app.configure(bg=bg_color)
    app.iconbitmap("Logo.ico")

    # Get screen dimensions
    screen_width = app.winfo_screenwidth()
    screen_height = app.winfo_screenheight()

    # Calculate position for centering
    width, height = 1000, 600
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    # Set window position and size
    app.geometry(f"{width}x{height}+{x}+{y}")

    # We'll show the window later after all components are initialized

    # Initialize loading overlay
    loading_overlay = LoadingOverlay(app)

    # Show loading screen during initialization
    loading_overlay.show("Loading... Initializing application...")

    # Force UI update to show loading screen
    app.update()

    # Initialize the rest of the application
    # This will be done in the main code after this function

    return app

# Check if another instance is already running
if not check_single_instance():
    # Another instance is already running, exit this one
    sys.exit()

# Initialize license manager first (before any windows are created)
license_manager = LicenseManager()

# Check if license is valid
if license_manager.check_license_validity():
    # License is already valid, directly create the main application
    app = create_main_application()
else:
    # Show license dialog (standalone window)
    license_verified = show_license_dialog()

    # Check again after dialog is closed
    if not license_manager.check_license_validity():
        # If still not valid, exit the application
        sys.exit()
    else:
        # License is now valid, create the main application
        app = create_main_application()

# Hide loading overlay without showing success dialog
loading_overlay.hide()
# Log the welcome message instead of showing a dialog
log_message("Access granted! Welcome to Grand Rp Helper.")

# Now setup custom styles after app creation
def setup_custom_styles():
    # Common button properties to ensure consistency across all themes
    button_font = ('Arial', 10)
    button_padding = 5
    button_width = 15
    button_relief = 'flat'
    button_borderwidth = 2

    # Define base button style with fixed dimensions
    app.style.configure(
        'Custom.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width  # Fixed width
    )

    # Define hover styles while maintaining dimensions
    app.style.configure(
        'CustomHover.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#2b3446'
    )

    app.style.configure(
        'CustomHover.success.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#28a745'
    )

    app.style.configure(
        'CustomHover.danger.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#dc3545'
    )

    # Define specific styles for recording button states
    app.style.configure(
        'RecordStart.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#28a745'  # Green background
    )

    app.style.configure(
        'RecordStop.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#dc3545'  # Red background
    )

    # Define specific styles for auto-press button states
    app.style.configure(
        'AutoPressStart.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#28a745'  # Green background
    )

    app.style.configure(
        'AutoPressStop.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#dc3545'  # Red background
    )

    # Define specific styles for AFK loop button states
    app.style.configure(
        'LoopStart.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#28a745'  # Green background
    )

    app.style.configure(
        'LoopStop.TButton',
        font=button_font,
        padding=button_padding,
        relief=button_relief,
        borderwidth=button_borderwidth,
        width=button_width,
        background='#dc3545'  # Red background
    )

    # Apply consistent button styling for all themes
    app.style.configure('TButton', font=button_font, padding=button_padding, width=button_width)
    app.style.configure('TButton.success', font=button_font, padding=button_padding, width=button_width)
    app.style.configure('TButton.danger', font=button_font, padding=button_padding, width=button_width)
    app.style.configure('TButton.info', font=button_font, padding=button_padding, width=button_width)
    app.style.configure('TButton.warning', font=button_font, padding=button_padding, width=button_width)

    # Ensure special button styles are always applied
    app.style.configure('RecordStart.TButton', background='#28a745')  # Green background
    app.style.configure('RecordStop.TButton', background='#dc3545')  # Red background
    app.style.configure('AutoPressStart.TButton', background='#28a745')  # Green background
    app.style.configure('AutoPressStop.TButton', background='#dc3545')  # Red background
    app.style.configure('LoopStart.TButton', background='#28a745')  # Green background
    app.style.configure('LoopStop.TButton', background='#dc3545')  # Red background

# Apply custom styles
setup_custom_styles()

# Force update all buttons to maintain consistent appearance
for widget in app.winfo_children():
    _update_widget_styles(widget)

# Left frame setup
left_frame = ttk.Frame(app)
left_frame.pack(side="left", fill="y", padx=10, pady=10)

def toggle_app_visibility():
    """Toggle the application visibility (hide/show)"""
    if not hasattr(app, '_hidden'):
        app._hidden = False

    if app._hidden:  # If currently hidden
        app.deiconify()  # Restore window
        app.attributes('-alpha', 1.0)  # Make fully visible
        app._hidden = False

        # Ensure window is brought to front
        app.lift()
        app.focus_force()

        # Flash the window to get user's attention
        try:
            ctypes.windll.user32.FlashWindow(ctypes.windll.user32.FindWindowW(None, app.title()), True)
        except Exception as e:
            log_message(f"Error flashing window: {str(e)}")

        log_message("Application restored")
    else:
        app.withdraw()  # Hide window completely (including from taskbar)
        app._hidden = True
        log_message("Application hidden from taskbar")

def exit_program():
    """Safely close the application"""
    global auto_press_running, loop_running, macro_running, app_mutex
    auto_press_running = False
    loop_running = False
    macro_running = False

    # Safely remove hotkeys with error handling
    try:
        keyboard.remove_hotkey(params["start_shortcut"])
    except KeyError:
        pass  # Hotkey wasn't registered

    try:
        keyboard.remove_hotkey(params["stop_shortcut"])
    except KeyError:
        pass  # Hotkey wasn't registered

    try:
        keyboard.remove_hotkey(params["hide_shortcut"])
    except KeyError:
        pass  # Hotkey wasn't registered

    # Release the mutex if it exists
    if app_mutex:
        try:
            win32api.CloseHandle(app_mutex)
        except Exception as e:
            print(f"Error closing mutex: {str(e)}")

    # Close window
    app.destroy()


# License Status Frame at the top




left_frame = ttk.Frame(app)
left_frame.pack(side="left", fill="y", padx=10, pady=10)

# Theme Selection Section
theme_frame = ttk.LabelFrame(left_frame, text="Theme Selection", bootstyle="info")
theme_frame.pack(fill="x", padx=10, pady=5)  # Add equal left & right spacing

theme_label = ttk.Label(
    theme_frame, text=f"Current Theme: {current_theme}", anchor="center"
)
theme_label.pack(fill="x", pady=5)  # Ensure label is centered

theme_combobox = ttk.Combobox(
    theme_frame, values=themes, state="readonly", justify="center"
)
theme_combobox.set(current_theme)
theme_combobox.pack(
    fill="x", padx=10, pady=5  # Ensure combobox has equal left & right padding
)
theme_combobox.bind("<<ComboboxSelected>>", change_theme)

# License Status Section
license_status_frame = ttk.LabelFrame(
    left_frame, text="License Status", bootstyle="info"
)
license_status_frame.pack(fill="x", padx=10, pady=5)

license_status_label = ttk.Label(
    license_status_frame,
    text="Checking license status...",
    bootstyle="info",
    font=("Helvetica", 10, "bold"),
    justify="center",
)
license_status_label.pack(pady=5, fill="x", expand=True)

# Application Settings Section
app_settings_frame = ttk.LabelFrame(
    left_frame, text="Application Settings", bootstyle="info"
)
app_settings_frame.pack(fill="x", padx=10, pady=5)

# Hide Application Shortcut Button
hide_shortcut_btn = CustomButton(
    app_settings_frame,
    text=f"Hide the application: {params['hide_shortcut'].upper()}",
    command=change_hide_shortcut,
    bootstyle="primary-outline",
    width=25
)
hide_shortcut_btn.pack(pady=5, padx=10, fill="x", expand=True)

# Email Settings Button
email_settings_btn = CustomButton(
    app_settings_frame,
    text="Email Notifications",
    command=lambda: show_email_settings(),
    bootstyle="info-outline",
    width=25
)
email_settings_btn.pack(pady=5, padx=10, fill="x", expand=True)

def show_email_settings():
    """Show email settings dialog."""
    # Create a new window for email settings but keep it hidden initially
    email_window = create_centered_window(
        app,
        title="Email Notification Settings",
        size="450x300"
    )
    email_window.iconbitmap("Logo.ico")
    email_window.grab_set()  # Make window modal

    # Load current email settings
    current_settings = email_manager.settings

    # Create main frame
    main_frame = ttk.Frame(email_window)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

    # Title
    ttk.Label(
        main_frame,
        text="Email Notification Settings",
        font=("Arial", 14, "bold")
    ).pack(pady=(0, 20))

    # Enable/Disable email notifications
    enable_var = tk.BooleanVar(value=current_settings.get("enabled", False))
    enable_frame = ttk.Frame(main_frame)
    enable_frame.pack(fill=tk.X, pady=5)

    ttk.Checkbutton(
        enable_frame,
        text="Enable Email Notifications",
        variable=enable_var,
        bootstyle="round-toggle"
    ).pack(side=tk.LEFT)

    # Fixed SMTP settings - hardcoded for security
    # Use Gmail as the default provider
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    use_tls = True
    smtp_username = "<EMAIL>"
    # Fixed SMTP password - hardcoded for security
    # This is the app password for the notification service
    # Password is not stored as a variable here for security reasons


    # Hidden variables for SMTP settings
    server_var = tk.StringVar(value=smtp_server)
    port_var = tk.IntVar(value=smtp_port)
    tls_var = tk.BooleanVar(value=use_tls)
    username_var = tk.StringVar(value=smtp_username)
    # Password is hardcoded for security and not editable by users

    # Email address
    address_frame = ttk.Frame(main_frame)
    address_frame.pack(fill=tk.X, pady=10)

    ttk.Label(address_frame, text="Your Email Address:").pack(side=tk.LEFT, padx=(0, 10))

    address_var = tk.StringVar(value=current_settings.get("address", ""))
    address_entry = ttk.Entry(address_frame, textvariable=address_var, width=30)
    address_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

    # Username (hidden, will be set to email address by default)
    username_var = tk.StringVar(value=current_settings.get("smtp_username", ""))

    # Notification Settings section
    notify_frame = ttk.LabelFrame(main_frame, text="Notification Settings")
    notify_frame.pack(fill=tk.X, pady=10)

    # Notify on crash
    crash_var = tk.BooleanVar(value=current_settings.get("notify_on_crash", True))
    crash_frame = ttk.Frame(notify_frame)
    crash_frame.pack(fill=tk.X, pady=5, padx=10)

    ttk.Checkbutton(
        crash_frame,
        text="Notify when selected application crashes",
        variable=crash_var,
        bootstyle="round-toggle"
    ).pack(side=tk.LEFT)



    # Buttons frame
    btn_frame = ttk.Frame(main_frame)
    btn_frame.pack(fill=tk.X, pady=20)

    def show_help():
        """Show help information about email settings."""
        help_text = """
Email Notification Setup Help

General Information:
- Email notifications will alert you when your selected application crashes
- Simply enter your email address where you want to receive notifications
- The system will automatically send notifications to your email

Troubleshooting:
- If you're not receiving emails, check your spam folder
- Make sure your email address is entered correctly
- Ensure you have an active internet connection


"""
        MessageDialog.show_info("Email Setup Help", help_text)

    def save_settings():
        """Save email settings and close dialog."""
        try:
            # Validate email address
            email = address_var.get().strip()
            if not email and enable_var.get():
                MessageDialog.show_error("Validation Error", "Please enter your email address.")
                return

            # Set username to email address if not provided
            username = username_var.get().strip()
            if not username:
                username = email

            # Create settings dictionary
            settings = {
                "enabled": enable_var.get(),
                "address": email,
                "smtp_server": server_var.get(),
                "smtp_port": port_var.get(),
                "smtp_username": username_var.get(),
                # Password is hardcoded in the EmailManager class for security
                "smtp_password": "pulk shfs fabf ycyi",
                "use_tls": tls_var.get(),
                "notify_on_crash": crash_var.get()
            }

            # Save settings
            if email_manager.save_email_settings(settings):
                MessageDialog.show_success("Settings Saved", "Email notification settings have been saved successfully.")
                email_window.destroy()
            else:
                MessageDialog.show_error("Save Error", "Failed to save email settings. Please try again.")
        except Exception as e:
            log_message(f"Error saving email settings: {str(e)}")
            MessageDialog.show_error("Error", f"An error occurred: {str(e)}")



    # Help button
    help_btn = ttk.Button(
        btn_frame,
        text="Help",
        command=show_help,
        bootstyle="secondary-outline",
        width=10
    )
    help_btn.pack(side=tk.LEFT, padx=10)

    # Save button
    save_btn = ttk.Button(
        btn_frame,
        text="Save Settings",
        command=save_settings,
        bootstyle="success-outline",
        width=15
    )
    save_btn.pack(side=tk.LEFT, padx=10)



    # Cancel button
    cancel_btn = ttk.Button(
        btn_frame,
        text="Cancel",
        command=email_window.destroy,
        bootstyle="danger-outline",
        width=15
    )
    cancel_btn.pack(side=tk.RIGHT, padx=10)

    # Show the window now that it's fully configured
    email_window.deiconify()



def record_shortcut(parent_window, title):
    shortcut = []
    recorded_keys = set()
    accepted_keys = {'ctrl', 'shift', 'alt', 'win', 'cmd', 'option', 'command'} | set(keyboard.all_modifiers)

    def on_key_press(event):
        key = event.name.lower()
        if key in accepted_keys or len(key) == 1:
            if key not in recorded_keys:
                recorded_keys.add(key)
                shortcut.append(key)
                update_display()

    def update_display():
        nonlocal shortcut
        seen = set()
        shortcut = [k for k in shortcut if not (k in seen or seen.add(k))]
        key_display.set('+'.join(shortcut))

    # Create popup window but keep it hidden initially
    popup = create_centered_window(
        parent_window,
        title=title,
        size="400x150"
    )
    popup.resizable(False, False)
    popup.iconbitmap("Logo.ico")  # Added logo icon

    # Use themed frame instead of direct style configuration
    main_frame = ttk.Frame(popup, style='dark.TFrame')
    main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Show the popup now that it's fully configured
    popup.deiconify()

    key_display = StringVar(value="Press keys...")

    ttk.Label(main_frame,
             text="Press your desired key combination:",
             style='inverse-primary.TLabel'
             ).pack(pady=10)

    entry = ttk.Entry(main_frame,
                     textvariable=key_display,
                     state='readonly',
                     font=('Arial', 12),
                     style='dark.TEntry')
    entry.pack(pady=5, padx=20, fill=tk.X)

    result = None

    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=10)


    def on_confirm():
        nonlocal result
        result = key_display.get()
        popup.destroy()

    def on_cancel():
        popup.destroy()

    keyboard.hook(on_key_press)

    ttk.Button(button_frame,
              text="OK",
              command=on_confirm,
              bootstyle="success",
              width=8).pack(side=tk.LEFT, padx=20)

    ttk.Button(button_frame,
              text="Cancel",
              command=on_cancel,
              bootstyle="danger",
              width=8).pack(side=tk.RIGHT, padx=20)

    popup.grab_set()
    popup.wait_window()
    keyboard.unhook(on_key_press)

    return result
def change_start_shortcut():
    global start_hotkey_handler, params

    # Disable buttons
    auto_press_button.config(state="disabled")
    interval_button.config(state="disabled")
    change_key_button.config(state="disabled")
    change_start_btn.config(state="disabled")
    app.update()

    try:
        new_shortcut = record_shortcut(app, "Record Start Shortcut")
        if new_shortcut and validate_shortcut(new_shortcut):
            change_start_btn.config(text=f"Start: {new_shortcut.upper()}")

            # Safely remove old shortcut if exists
            try:
                keyboard.remove_hotkey(params["start_shortcut"])
            except KeyError:
                pass

            params["start_shortcut"] = new_shortcut.lower()
            save_parameters(params)
            keyboard.add_hotkey(params["start_shortcut"], toggle_auto_press)
            log_message(f"Start shortcut updated to '{new_shortcut}'")
    finally:
        auto_press_button.config(state="normal")
        interval_button.config(state="normal")
        change_key_button.config(state="normal")
        change_start_btn.config(state="normal")


def change_stop_shortcut():
    new_shortcut = record_shortcut(app, "Record Stop Shortcut")
    if new_shortcut and validate_shortcut(new_shortcut):
        keyboard.remove_hotkey(params["stop_shortcut"])
        params["stop_shortcut"] = new_shortcut.lower()


        save_parameters(params)
        keyboard.add_hotkey(params["stop_shortcut"], stop_macro)
        log_message(f"Stop shortcut updated to '{new_shortcut}'")

def validate_shortcut(shortcut):
    try:
        keyboard.parse_hotkey(shortcut)
        return True
    except ValueError:
        MessageDialog.show_error("Invalid Shortcut", "The entered shortcut contains invalid keys")
        return False


def change_stop_shortcut():
    new_shortcut = simpledialog.askstring(
        "Change Stop Shortcut",
        "Enter new stop shortcut (e.g. ctrl+shift+p):",
        initialvalue=params["stop_shortcut"],
    )
    if new_shortcut:
        keyboard.remove_hotkey(params["stop_shortcut"])
        params["stop_shortcut"] = new_shortcut.lower()
        save_parameters(params)
        keyboard.add_hotkey(params["stop_shortcut"], toggle_auto_press)
        log_message(f"Stop shortcut updated to '{new_shortcut}'")


def simulate_key_press():
    pyautogui.press(params["key_to_press"])
    log_message(f"Pressed key: {params['key_to_press']}")


def toggle_auto_press():
    global auto_press_running
    if auto_press_running:
        auto_press_running = False
        auto_press_button.config(text="Start Auto-Press", bootstyle=SUCCESS, style='AutoPressStart.TButton')
        # Re-enable the buttons when stopping
        change_key_button.config(state="normal")
        interval_button.config(state="normal")
        log_message("Auto key pressing stopped.")
    else:
        auto_press_running = True
        auto_press_button.config(text="Stop Auto-Press", bootstyle=DANGER, style='AutoPressStop.TButton')
        # Disable the buttons when starting
        change_key_button.config(state="disabled")
        interval_button.config(state="disabled")
        log_message("Auto key pressing started.")
        threading.Thread(target=auto_press_loop, daemon=True).start()


def auto_press_loop():
    # Cache the key to press and interval to avoid dictionary lookups in the loop
    key_to_press = params["key_to_press"]
    interval = params["click_interval"]

    while auto_press_running:
        try:
            # Use pyautogui directly instead of calling simulate_key_press()
            # to avoid function call overhead
            pyautogui.press(key_to_press)

            # Log less frequently to reduce overhead
            if random.random() < 0.1:  # Only log approximately 10% of presses
                log_message(f"Pressed key: {key_to_press}")

            # Interruptible sleep
            end_time = time.time() + interval
            while time.time() < end_time and auto_press_running:
                time.sleep(min(0.1, interval))  # Use shorter sleep for small intervals
        except Exception as e:
            log_message(f"Error in auto press loop: {str(e)}")
            time.sleep(0.5)  # Brief pause on error


def change_interval():
    # Create window but keep it hidden initially
    new_interval_window = create_centered_window(
        app,
        window_class=Toplevel,
        title="Change Interval",
        size="300x150"
    )
    new_interval_window.iconbitmap("Logo.ico")
    new_interval_window.resizable(False, False)

    Label(new_interval_window, text="Enter the key press interval (in seconds):").pack(pady=10)

    interval_entry = Entry(new_interval_window)
    interval_entry.insert(0, str(params["click_interval"]))
    interval_entry.pack(pady=5)

    def save_interval():
        try:
            new_interval = float(interval_entry.get())
            if new_interval < 0.01:
                messagebox.showerror("Error", "Interval must be at least 0.01 seconds")
                return
            params["click_interval"] = new_interval
            save_parameters(params)
            log_message(f"Key press interval updated to {new_interval} seconds.")
            new_interval_window.destroy()
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")

    Button(new_interval_window, text="Save", command=save_interval).pack(pady=10)
    new_interval_window.grab_set()

    # Show the window now that it's fully configured
    new_interval_window.deiconify()

def change_key():
    # Define validator function
    def validate_key(value):
        return value.strip() != ""

    # Show advanced input dialog
    new_key = MessageDialog.show_input_dialog(
        title="Change Key to Press",
        message="Enter the key to press:",
        default_value=params["key_to_press"],
        validator=validate_key,
        error_message="Please enter a valid key"
    )

    # Process result if not canceled
    if new_key is not None:
        new_key = new_key.strip().lower()
        params["key_to_press"] = new_key
        save_parameters(params)
        log_message(f"Key to press updated to '{new_key}'.")

def change_interaction_key():
    # Define validator function
    def validate_key(value):
        return value.strip() != ""

    # Show advanced input dialog
    new_key = MessageDialog.show_input_dialog(
        title="Change Interaction Key",
        message="Enter the key to spam:",
        default_value=params["interaction_key"],
        validator=validate_key,
        error_message="Please enter a valid key"
    )

    # Process result if not canceled
    if new_key is not None:
        new_key = new_key.strip().lower()
        params["interaction_key"] = new_key
        save_parameters(params)
        log_message(f"Interaction key updated to '{new_key}'.")

def change_return_delay():
    # Define validator function
    def validate_delay(value):
        return value >= 0.1

    # Show advanced input dialog
    new_delay = MessageDialog.show_input_dialog(
        title="Change Return Delay",
        message="Enter the delay before returning (in seconds):",
        default_value=params["return_delay"],
        input_type="number",
        validator=validate_delay,
        error_message="Delay must be at least 0.1 seconds"
    )

    # Process result if not canceled
    if new_delay is not None:
        params["return_delay"] = new_delay
        save_parameters(params)
        log_message(f"Return delay updated to {new_delay} seconds.")


# macro functions
macro_usage = {}
last_timestamp = None  # Stores the time of the last recorded action


def record_action(
    action_type,
    details,
    timestamp,

):
    global recorded_steps, last_timestamp

    # Calculate delay: Time elapsed since the last recorded action
    delay = (timestamp - last_timestamp) if last_timestamp else 0
    last_timestamp = timestamp  # Update last recorded timestamp

    recorded_steps.append(
        {
            "type": action_type,
            "details": details,
            "delay": round(delay, 2),  # Store delay with 2 decimal places
            "duration": 0,  # Initially 0, will be updated later
        }
    )

      # Debugging print
    log_message(f"Action recorded: {action_type} (duration will be calculated on release)")


# Function to start recording
def start_recording():
    global recording, recorded_steps, listener_active, press_times, mouse_listener, keyboard_listener, last_timestamp

    with lock:
        if not recording:
            recorded_steps = []
            last_timestamp = None  # <-- ✅ هذا هو المطلوب

        press_times = {}

    recording = True
    listener_active = True
    log_message("Recording started...")

    def on_click(x, y, button, pressed):
        if not recording:
            return

        key = f"mouse_{button}"
        current_time = time.time()
        with lock:
            if pressed:
                record_action(
                    "mouse_click", {"x": x, "y": y, "button": button.name}, current_time
                )
                press_times[key] = (current_time, len(recorded_steps) - 1)
            else:
                if key in press_times:
                    start_time, idx = press_times[key]
                    press_duration = round(current_time - start_time, 2)
                    recorded_steps[idx]["duration"] = press_duration
                    # Log the updated duration
                    print(f"Updated Action Duration: {recorded_steps[idx]}")
                    log_message(f"Mouse {button.name} clicked for {press_duration} seconds")
                    del press_times[key]

    def on_press(key):
        if not recording:
            return

        try:
            key_str = key.name
        except AttributeError:
            key_str = key.char  # Handle regular characters

        current_time = time.time()
        key_id = f"keyboard_{key_str}"

        with lock:
            # ✅ Only record key press if it's NOT already pressed (prevent spam issue)
            if key_id not in press_times:
                record_action("key_press", {"key": key_str}, current_time)
                press_times[key_id] = (current_time, len(recorded_steps) - 1)

    def on_release(key):
        if not recording:
            return

        try:
            key_str = key.name
        except AttributeError:
            key_str = key.char

        key_id = f"keyboard_{key_str}"
        current_time = time.time()

        with lock:
            # ✅ Ensure duration is only calculated if key was actually pressed
            if key_id in press_times:
                start_time, idx = press_times[key_id]
                press_duration = round(current_time - start_time, 2)
                recorded_steps[idx]["duration"] = press_duration
                # Log the updated duration
                print(f"Updated Action Duration: {recorded_steps[idx]}")
                log_message(f"Key {key_str} pressed for {press_duration} seconds")
                del press_times[key_id]  # Remove after processing

    # Stop existing listeners if any
    stop_listeners()

    # Start new listeners
    mouse_listener = mouse.Listener(on_click=on_click)
    keyboard_listener = pynput_keyboard.Listener(
        on_press=on_press, on_release=on_release
    )
    mouse_listener.start()
    keyboard_listener.start()


def stop_listeners():
    global mouse_listener, keyboard_listener
    if mouse_listener is not None:
        mouse_listener.stop()
        mouse_listener = None
    if keyboard_listener is not None:
        keyboard_listener.stop()
        keyboard_listener = None


# Function to stop recording
def stop_recording():
    global recording, listener_active
    recording = False
    listener_active = False
    log_message("Recording stopped.")


# Function to save the recorded macro
def save_macro(name):
    global macros, macro_usage, recorded_steps
    if not recorded_steps:
        log_message("No actions recorded to save.")
        return

    macros[name] = recorded_steps[:]
    macro_usage[name] = 0  # Initialize usage count
    save_macros(macros)
    with open("macro_usage.json", "w") as f:
        json.dump(macro_usage, f)
    log_message(f"Macro '{name}' saved successfully.")
    recorded_steps = []


def show_save_macro_dialog():
    """Shows a dialog window for saving the macro with a name."""
    # Create window but keep it hidden initially
    save_window = create_centered_window(
        app,
        window_class=Toplevel,
        title="Save Macro",
        size="300x150"
    )
    save_window.iconbitmap("Logo.ico")
    save_window.grab_set()  # Make window modal

    ttk.Label(save_window, text="Enter a name for your macro:", font=("Arial", 10)).pack(pady=10)

    name_var = StringVar()
    name_entry = ttk.Entry(save_window, textvariable=name_var)
    name_entry.pack(pady=10)
    name_entry.focus()  # Auto-focus the entry field

    def save():
        name = name_var.get().strip()
        if not name:
            MessageDialog.show_error("Error", "Please enter a name for the macro")
            return
        save_macro(name)
        save_window.destroy()

    def cancel():
        if MessageDialog.show_confirm("Cancel", "Are you sure you want to discard this recording?"):
            save_window.destroy()

    button_frame = ttk.Frame(save_window)
    button_frame.pack(pady=10)

    ttk.Button(button_frame, text="Save", command=save, bootstyle="success").pack(side=LEFT, padx=10)
    ttk.Button(button_frame, text="Cancel", command=cancel, bootstyle="danger").pack(side=LEFT, padx=10)

    # Allow saving with Enter key
    # Using _ to indicate intentionally unused parameter
    name_entry.bind('<Return>', lambda _: save())
    save_window.protocol("WM_DELETE_WINDOW", cancel)  # Handle window close button

    # Show the window now that it's fully configured
    save_window.deiconify()

    # Set focus after the window is visible
    name_entry.focus()


def toggle_recording():
    global recording, recorded_steps
    if not recording:
        start_recording()
        record_button.config(text="Stop Recording", bootstyle=DANGER, style='RecordStop.TButton')
        # Reset recorded steps when starting new recording
        recorded_steps = []
        # last_timestamp is used in the recording logic elsewhere
    else:
        stop_recording()
        record_button.config(text="Start Recording", bootstyle=SUCCESS, style='RecordStart.TButton')
        if recorded_steps:  # Only show save dialog if there are recorded steps
            show_save_macro_dialog()


macro_running = False

macro_stop_event = threading.Event()
# Function to stop the macro
def stop_macro():
    global macro_running, macro_stop_event
    macro_running = False
    macro_stop_event.set()  # Signal the macro to stop immediately
    log_message("Macro stopped.")
    # Force re-enable buttons if macro was stopped prematurely
    record_button.config(state="normal")
    play_button.config(state="normal")
    stop_macro_button.config(state="disabled")
    macro_stop_event.clear() # clear the event for the next run.


# Function to play the recorded macro
def play_macro():
    global macro_running, macros, macro_usage

    # Load usage data
    try:
        with open("macro_usage.json", "r") as f:
            macro_usage = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        macro_usage = {}

    # Create popup window but keep it hidden initially
    popup = create_centered_window(
        app,
        window_class=Toplevel,
        title="Select Macro to Play",
        size="400x300"
    )
    popup.iconbitmap("Logo.ico")  # Added logo icon

    # Sort macros by usage (most used first)
    sorted_macros = sorted(
        macros.keys(), key=lambda x: macro_usage.get(x, 0), reverse=True
    )

    # Listbox with scrollbar
    list_frame = ttk.Frame(popup)
    list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    scrollbar = ttk.Scrollbar(list_frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    macro_list = Listbox(
        list_frame,
        yscrollcommand=scrollbar.set,
        bg="#23272a",
        fg="white",
        font=("Consolas", 12),
    )
    macro_list.pack(fill=tk.BOTH, expand=True)
    scrollbar.config(command=macro_list.yview)

    # Populate list with macros
    for name in sorted_macros:
        usage_count = macro_usage.get(name, 0)
        macro_list.insert(tk.END, f"{name} (Used {usage_count}x)")

    # Confirm button
    def on_select():
        selection = macro_list.curselection()
        if selection:
            selected_name = sorted_macros[selection[0]]
            popup.destroy()
            execute_macro(selected_name)

    ttk.Button(popup, text="Select", command=on_select).pack(pady=10)

    # Show the popup now that it's fully configured
    popup.deiconify()

    # Make the popup modal
    popup.grab_set()
    popup.wait_window()


def execute_macro(name):
    global macro_running, macro_usage, macro_stop_event

    if name not in macros:
        log_message("Macro not found.")
        return

    # Update usage count
    macro_usage[name] = macro_usage.get(name, 0) + 1
    with open("macro_usage.json", "w") as f:
        json.dump(macro_usage, f)

    # Get repeat count using custom dialog
    repeat = get_repeat_count(name)
    if not repeat:
        return

    # Disable all macro buttons
    record_button.config(state="disabled")
    play_button.config(state="disabled")
    stop_macro_button.config(state="normal")  # Keep stop button enabled

    log_message(f"Starting '{name}' in 3 seconds...")
    time.sleep(3)

    def run_macro():
        global macro_running
        macro_running = True
        # إضافة إيقاف عبر اختصار
        params = load_parameters()
        stop_hotkey = params.get("stop_shortcut", "ctrl+shift+e")
        keyboard.add_hotkey(stop_hotkey, stop_macro)

        try:
            for _ in range(repeat):
                if not macro_running or macro_stop_event.is_set():
                    break
                # Execute each step in the macro
                for step in macros[name]:
                    if not macro_running or macro_stop_event.is_set():
                        break
                    if step["type"] == "mouse_click":
                        button = step["details"]["button"].lower()
                        x = step["details"]["x"]
                        y = step["details"]["y"]
                        pyautogui.mouseDown(x, y, button=button)
                        time.sleep(step["duration"])
                        pyautogui.mouseUp(x, y, button=button)
                    elif step["type"] == "key_press":
                        key = step["details"]["key"].strip("'")
                        pyautogui.keyDown(key)
                        time.sleep(step["duration"])
                        pyautogui.keyUp(key)
                    time.sleep(step["delay"])
        finally:
            # Re-enable buttons when done
            record_button.config(state="normal")
            play_button.config(state="normal")
            stop_macro_button.config(state="disabled")
            macro_running = False
            macro_stop_event.clear()
            keyboard.remove_hotkey(stop_hotkey)
 # clear the stop event

    threading.Thread(target=run_macro, daemon=True).start()


# Cache for button timer to avoid unnecessary updates
_last_timer_text = ""
_last_timer_update = 0

def update_button_timer():
    """Update the button timer with optimized UI updates"""
    global _last_timer_text, _last_timer_update

    if not loop_running:
        # Only update UI if needed
        if _last_timer_text != "Start Loop":
            btn_start_loop.config(text="Start Loop", state="normal", style='LoopStart.TButton')
            btn_stop_loop.config(state="disabled")
            _last_timer_text = "Start Loop"
        return

    # Get current time only once
    current_time = time.time()

    # Only update every 500ms to reduce UI overhead
    if current_time - _last_timer_update < 0.5:
        app.after(100, update_button_timer)
        return

    with timer_lock:
        current_end_time = sleep_end_time

    if current_end_time:
        remaining = max(0, current_end_time - current_time)
        remaining_str = time.strftime("%H:%M:%S", time.gmtime(remaining))
        new_text = f"Next in: {remaining_str}"

        # Only update if text has changed
        if new_text != _last_timer_text:
            # Keep the disabled state but update text
            btn_start_loop.config(text=new_text)
            _last_timer_text = new_text
    else:
        if _last_timer_text != "Processing...":
            # Keep the disabled state but update text
            btn_start_loop.config(text="Processing...")
            _last_timer_text = "Processing..."

    _last_timer_update = current_time
    app.after(100, update_button_timer)  # Check more frequently but update less frequently


##############################AFK system#####################################


# Application Interaction Functions
def app_interface(app):
    global btn_stop_loop
    global btn_start_loop
    global selected_app
    global btn_change_interaction_key
    global btn_change_return_delay
    global btn_list_apps

    # Application Interaction Frame
    app_interaction_frame = ttk.LabelFrame(app, text="Afk System", bootstyle=SECONDARY)
    app_interaction_frame.pack(fill=tk.X, padx=10, pady=5)

    # Button to start the game switch loop
    btn_start_loop = CustomButton(
        app_interaction_frame,
        text="Start Loop",
        command=start_loop,
        bootstyle=SUCCESS,
        style='LoopStart.TButton'
    )
    btn_start_loop.pack(side=tk.LEFT, padx=5, pady=5)

    # Button to stop the game switch loop
    btn_stop_loop = CustomButton(
        app_interaction_frame,
        text="Stop Loop",
        command=stop_loop,
        bootstyle=DANGER,
        state="disabled",
        style='LoopStop.TButton'
    )
    btn_stop_loop.pack(side=tk.LEFT, padx=5, pady=5)

    # Button to change the interaction key
    btn_change_interaction_key = CustomButton(
        app_interaction_frame,
        text="Change Key",
        command=change_interaction_key,
        bootstyle=WARNING
    )
    btn_change_interaction_key.pack(side=tk.LEFT, padx=5, pady=5)

    # Button to change the return delay
    btn_change_return_delay = CustomButton(
        app_interaction_frame,
        text="Change Return Delay",
        command=change_return_delay,
        bootstyle=PRIMARY
    )
    btn_change_return_delay.pack(side=tk.LEFT, padx=5, pady=5)

    # Button to change the auto-type text
    btn_list_apps = CustomButton(
        app_interaction_frame,
        text="Choose App",
        command=lambda: show_app_list_and_choose(app),
        bootstyle=INFO
    )
    btn_list_apps.pack(side=tk.LEFT, padx=5, pady=5)




def show_app_list_and_choose(app):
    """
    Displays a list of running apps and allows the user to choose one.
    """
    global selected_app
    apps = [app for app in gw.getAllTitles() if app.strip()]
    if not apps:
        log_message("No running apps found.")
        return

    popup = create_popup("Choose Macro", "400x300")  # Using create_popup which already has icon


    label = ttk.Label(popup, text="Choose an app to interact with:")
    label.pack(pady=10)

    listbox = tk.Listbox(popup, bg="#23272a", fg="white", font=("Consolas", 10))
    listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    # Populate the listbox with running apps
    for app in apps:
        listbox.insert(tk.END, app)

    def on_select():
        global selected_app
        selection = listbox.curselection()
        if selection:
            selected_app = listbox.get(selection[0])
            log_message(f"Selected: {selected_app}")
            save_selected_app(selected_app)
            popup.destroy()

    # Button to confirm the selection
    btn_select = ttk.Button(popup, text="Select", command=on_select)
    btn_select.pack(pady=10)


loop_count = 0
total_time_spent = 0
start_time = 0


def start_loop():
    global loop_running, loop_count, total_time_spent, start_time, btn_stop_loop
    if not selected_app:
        log_message("No app selected. Choose an app first.")
        return

    loop_running = True
    loop_count = 0
    total_time_spent = 0
    start_time = time.time()

    # Disable start button and other controls
    btn_start_loop.config(state="disabled")
    btn_stop_loop.config(state="normal", style='LoopStop.TButton')  # Enable stop button with red style

    # Disable the change key, return delay, and choose app buttons
    btn_change_interaction_key.config(state="disabled")
    btn_change_return_delay.config(state="disabled")
    btn_list_apps.config(state="disabled")

    update_button_timer()  # Start the countdown updates
    threading.Thread(target=GameSwitch, daemon=True).start()


def stop_loop():
    global loop_running
    loop_running = False
    log_message("Loop stopped.")
    log_final_report()

    # Reset and re-enable all buttons
    btn_start_loop.config(state="normal", text="Start Loop", style='LoopStart.TButton')  # Reset start button with green style
    btn_stop_loop.config(state="disabled")  # Disable stop button

    # Re-enable the buttons that were disabled when starting the loop
    btn_change_interaction_key.config(state="normal")
    btn_change_return_delay.config(state="normal")
    btn_list_apps.config(state="normal")


def GameSwitch():
    global loop_running, params, loop_count, total_time_spent, sleep_end_time

    game_title = load_selected_app()
    desktop_title = "Program Manager"

    # Cache parameters to avoid dictionary lookups in the loop
    interaction_key = params["interaction_key"]
    return_delay = params["return_delay"]

    while loop_running:
        loop_count += 1
        loop_start = time.time()


        # Step 1-3: Perform key presses and focus changes
        try:
            # Check if the application is still running
            app_windows = gw.getWindowsWithTitle(game_title)
            if not app_windows:
                log_message(f"WARNING: Application '{game_title}' not found. It may have crashed.")
                if email_manager.send_crash_notification(game_title):
                    log_message("Email notification sent about application crash.")
                print(f"Stopping loop due to application crash.")

                break
            # Batch operations to reduce context switches
            focus_window(game_title)
            keyboard_controller.press(interaction_key)

            # Use a more efficient sleep approach with periodic checks
            end_time = time.time() + 2
            while time.time() < end_time and loop_running:
                time.sleep(0.1)  # Short sleep to allow for interruption

            if not loop_running:
                break

            keyboard_controller.release(interaction_key)

            # Short sleep with interruption check
            end_time = time.time() + 1
            while time.time() < end_time and loop_running:
                time.sleep(0.1)

            if not loop_running:
                break

            focus_window(desktop_title)

            # Step 4: Sleep phase with timer tracking
            with timer_lock:
                sleep_end_time = time.time() + return_delay  # Set sleep end time

            log_message(f"Sleeping for {return_delay} seconds...")

            # Interruptible sleep that checks loop_running periodically
            end_time = time.time() + return_delay
            while time.time() < end_time and loop_running:
                # Check if the application is still running every 5 seconds
                if time.time() % 5 < 0.5:  # Check approximately every 5 seconds
                    app_windows = gw.getWindowsWithTitle(game_title)
                    if not app_windows:
                        app.after(0, stop_loop)  # Use after(0) to call from main thread
                        break

                time.sleep(0.5)  # Check every half second if we should stop

            with timer_lock:
                sleep_end_time = None  # Reset after sleep

        except Exception as e:
            log_message(f"Error in GameSwitch loop: {str(e)}")
            time.sleep(1)  # Brief pause on error before continuing

        if not loop_running:
            break

        loop_duration = time.time() - loop_start
        total_time_spent += loop_duration



def log_final_report():
    """Logs a final report on the loop execution immediately after stopping."""
    end_time = time.time()
    total_runtime = end_time - start_time if start_time else 0
    avg_loop_time = (total_time_spent / loop_count) if loop_count > 0 else 0

    log_message("===== FINAL REPORT =====")
    log_message(f"Total loops executed: {loop_count}")
    log_message(f"Total runtime: {total_runtime:.2f} seconds")
    log_message(f"Total time spent in loops: {total_time_spent:.2f} seconds")
    log_message(f"Average loop duration: {avg_loop_time:.2f} seconds")
    log_message("========================")


def change_interaction_key():
    """
    Stops the loop, allows the user to change the interaction key,
    then restarts the loop if it was running.
    """
    global loop_running
    was_running = loop_running

    # Stop the loop
    if loop_running:
        log_message("Stopping the loop to change the interaction key...")
        loop_running = False
        time.sleep(1)  # Small delay to ensure the loop stops

    # Define validator function
    def validate_key(value):
        return value.strip() != ""

    # Show advanced input dialog
    new_key = MessageDialog.show_input_dialog(
        title="Change Interaction Key",
        message="Enter the key to spam (e.g., 'd', 'a', etc.):",
        default_value=params["interaction_key"],
        validator=validate_key,
        error_message="Please enter a valid key"
    )

    # Process result if not canceled
    if new_key is not None:
        new_key = new_key.strip().lower()
        params["interaction_key"] = new_key
        save_parameters(params)
        log_message(f"Interaction key updated to '{new_key}'.")

    # Restart the loop if it was running before
    if was_running and new_key is not None:
        start_loop()


def change_return_delay():
    """
    Allows the user to change the return delay.
    """
    # Define validator function
    def validate_delay(value):
        return value >= 0.1

    # Show advanced input dialog
    new_delay = MessageDialog.show_input_dialog(
        title="Change Return Delay",
        message="Enter the delay before returning app(in seconds):",
        default_value=params["return_delay"],
        input_type="number",
        validator=validate_delay,
        error_message="Delay must be at least 0.1 seconds"
    )

    # Process result if not canceled
    if new_delay is not None:
        params["return_delay"] = new_delay
        save_parameters(params)
        log_message(f"Return delay updated to {new_delay} seconds.")


# Add this import at the top of your script


def save_selected_app(app_title):
    """
    Saves the selected app to a file and updates cache.
    """
    global _cached_app, _app_loaded

    try:
        # Update cache
        _cached_app = app_title
        _app_loaded = True

        # Save to file
        with open(APP_FILE, "wb") as file:
            pickle.dump(app_title, file)
        log_message(f"Saved selected app: {app_title}")
    except Exception as e:
        log_message(f"Error saving selected app: {str(e)}")


# Cache for selected app
_cached_app = None
_app_loaded = False

def load_selected_app():
    """
    Loads the selected app from a file with caching.
    """
    global _cached_app, _app_loaded

    # Return cached app if already loaded
    if _app_loaded:
        return _cached_app

    try:
        with open(APP_FILE, "rb") as file:
            app_title = pickle.load(file)
        # Only log on first load to reduce console spam
        if not _app_loaded:
            log_message(f"Loaded selected app: {app_title}")
        _cached_app = app_title
        _app_loaded = True
        return app_title
    except FileNotFoundError:
        if not _app_loaded:
            log_message("No saved app found.")
        _app_loaded = True
        return None
    except Exception as e:
        if not _app_loaded:
            log_message(f"Error loading selected app: {str(e)}")
        _app_loaded = True
        return None


def focus_window(window_title):
    """
    Focuses on a specific window by its title.
    """
    try:
        window = gw.getWindowsWithTitle(window_title)[
            0
        ]  # Get the first matching window
        if window:
            if window.isMinimized:
                window.restore()  # Restore the window if minimized
            window.activate()  # Activate the window
            time.sleep(1)  # Wait for the window to focus
            log_message(f"Focused on window: {window_title}")
    except IndexError:
        log_message(f"Could not find window with title: {window_title}")


# Shared Functions


# Buffer for log messages to reduce UI updates
_log_buffer = []
_last_log_update = 0
_log_update_interval = 0.5  # Update logs every 0.5 seconds

def log_message(message):
    """Add message to log with buffering to reduce UI updates"""
    global _log_buffer, _last_log_update

    # Add message to buffer
    timestamp = datetime.now().strftime('%H:%M:%S')
    _log_buffer.append(f"[{timestamp}] {message}\n")

    # Only update UI periodically to reduce overhead
    current_time = time.time()
    if current_time - _last_log_update >= _log_update_interval:
        _flush_log_buffer()
        _last_log_update = current_time

    # Schedule a flush if this is the first message in a while
    if len(_log_buffer) == 1 and hasattr(app, 'after'):
        app.after(int(_log_update_interval * 1000), _flush_log_buffer)

def _flush_log_buffer():
    """Flush buffered log messages to the UI"""
    global _log_buffer

    if not _log_buffer or not hasattr(log_area, 'winfo_exists') or not log_area.winfo_exists():
        return

    # Combine all messages and update once
    combined_message = ''.join(_log_buffer)
    _log_buffer = []

    # Update the log area
    log_area.config(state=tk.NORMAL)  # Enable editing
    log_area.insert(tk.END, combined_message)
    log_area.config(state=tk.DISABLED)  # Disable editing
    log_area.see(tk.END)  # Scroll to the latest message


# UI Setup


############################ Auto Key Presser Frame###################################
auto_press_frame = ttk.LabelFrame(app, text="Auto Key Presser", bootstyle=INFO)
auto_press_frame.pack(fill=tk.X, padx=10, pady=5)


auto_press_button = CustomButton(
    auto_press_frame,
    text="Start Auto-Press",
    command=toggle_auto_press,
    bootstyle=SUCCESS,
    style='AutoPressStart.TButton'
)
auto_press_button.pack(side=tk.LEFT, padx=5, pady=5)

interval_button = CustomButton(
    auto_press_frame,
    text="Change Interval",
    command=change_interval,
    bootstyle=INFO
)
interval_button.pack(side=tk.LEFT, padx=5, pady=5)

change_key_button = CustomButton(
    auto_press_frame,
    text="Change Key",
    command=change_key,
    bootstyle=WARNING
)
change_key_button.pack(side=tk.LEFT, padx=5, pady=5)

change_start_btn = CustomButton(
    auto_press_frame,
    text=f"Shortcut: {params['start_shortcut'].upper()}",
    command=change_start_shortcut,
    bootstyle="info-outline",
    width=20
)
change_start_btn.pack(side=tk.LEFT, padx=5, pady=5)


############################ end Auto Key Presser Frame ########################################################


############################ Macro Recorder Frame ##############################################################
macro_frame = ttk.LabelFrame(app, text="Macro Recorder", bootstyle=PRIMARY)
macro_frame.pack(fill=tk.X, padx=10, pady=5)


# Variable for macro name entry
macro_name_var = StringVar()
def change_macro_stop_shortcut():
    global params

    new_shortcut = record_shortcut(app, "Record Macro Stop Shortcut")
    if new_shortcut and validate_shortcut(new_shortcut):
        # إزالة القديم بأمان
        try:
            keyboard.remove_hotkey(params["stop_shortcut"])
        except KeyError:
            pass

        # حفظ وتحديث
        params["stop_shortcut"] = new_shortcut.lower()
        save_parameters(params)

        # إضافة الاختصار الجديد
        keyboard.add_hotkey(params["stop_shortcut"], stop_macro)

        # تحديث النص على الزر
        change_macro_stop_btn.config(text=f"Stop Macro: {new_shortcut.upper()}")
        log_message(f"Stop macro shortcut updated to '{new_shortcut}'")

record_button = CustomButton(
    macro_frame,
    text="Start Recording",
    command=toggle_recording,
    bootstyle=SUCCESS,
    style='RecordStart.TButton'
)
record_button.pack(side=tk.LEFT, padx=5, pady=5)

play_button = CustomButton(
    macro_frame,
    text="Play Macro",
    command=play_macro,
    bootstyle=PRIMARY
)
play_button.pack(side=tk.LEFT, padx=5, pady=5)

stop_macro_button = CustomButton(
    macro_frame,
    text="Stop Macro",
    command=stop_macro,
    bootstyle=DANGER,
    state="disabled"
)
stop_macro_button.pack(side=tk.LEFT, padx=5, pady=5)

view_macros_button = CustomButton(
    macro_frame, text="View Macros", command=view_macros, bootstyle=INFO
)
view_macros_button.pack(side=tk.LEFT, padx=5, pady=5)

change_macro_stop_btn = CustomButton(
    macro_frame,
    text=f"Stop Macro: {params['stop_shortcut'].upper()}",
    command=change_macro_stop_shortcut,
    bootstyle="danger-outline",
    width=20
)
change_macro_stop_btn.pack(side=tk.LEFT, padx=5, pady=5)

# Add the App Interaction functionality
app_interface(app)

# Admin button (only visible for admin users)
def show_admin_panel():
    """Show the admin panel for administrative functions"""
    if not license_manager.is_admin():
        MessageDialog.show_error("Access Denied", "You need administrative privileges to access this feature.")
        return

    # Reinitialize Firebase with admin credentials for full access
    try:
        # We need to delete the existing app first
        for app_name in list(firebase_admin._apps.keys()):
            firebase_admin.delete_app(firebase_admin.get_app(app_name))

        # Reset the initialization flag
        global _firebase_initialized
        _firebase_initialized = False

        # Initialize with admin credentials
        if not initialize_firebase(is_admin=True):
            raise Exception("Failed to initialize Firebase with admin credentials")

        log_message("Firebase reinitialized with admin credentials")
    except Exception as e:
        log_message(f"Error reinitializing Firebase with admin credentials: {str(e)}")
        MessageDialog.show_error("Admin Access Error", "Failed to initialize admin access. Please check your configuration.")
        return

    # Create admin window but keep it hidden initially
    admin_window = create_centered_window(
        app,
        title="Admin Panel",
        size="500x400"
    )
    admin_window.iconbitmap("Logo.ico")
    admin_window.grab_set()

    # Show the window now that it's fully configured
    admin_window.deiconify()

    # Add a cleanup function to reinitialize with user credentials when admin panel is closed
    def on_admin_window_close():
        admin_window.destroy()
        # Reinitialize with user credentials
        try:
            # Delete admin app
            for app_name in list(firebase_admin._apps.keys()):
                firebase_admin.delete_app(firebase_admin.get_app(app_name))

            # Reset the initialization flag
            global _firebase_initialized
            _firebase_initialized = False

            # We don't need to initialize Firebase immediately
            # It will be initialized lazily when needed
            log_message("Admin session ended")
        except Exception as e:
            log_message(f"Error cleaning up admin session: {str(e)}")

    # Set the close handler
    admin_window.protocol("WM_DELETE_WINDOW", on_admin_window_close)

    ttk.Label(admin_window, text="Admin Control Panel", font=("Arial", 16, "bold")).pack(pady=20)

    # Create license frame
    license_frame = ttk.LabelFrame(admin_window, text="License Management")
    license_frame.pack(fill=tk.X, padx=20, pady=10)

    # Generate license
    def generate_license():
        try:
            days = int(days_var.get())
            if days <= 0:
                messagebox.showerror("Error", "Days must be a positive number")
                return

            role = role_var.get()

            # Call Firebase to generate license
            ref = db.reference('licenses')

            # Generate a random license key (XXXX-XXXX-XXXX-XXXX format)
            chars = string.ascii_uppercase + string.digits
            segments = [''.join(secrets.choice(chars) for _ in range(4)) for _ in range(4)]
            license_key = '-'.join(segments)

            # Create license data - expiry will be calculated when activated
            license_data = {
                'validity_days': days,  # Store the number of days instead of expiry date
                'expiry_date': None,    # Will be set when license is activated
                'is_active': True,
                'device_id': None,
                'created_date': datetime.now().isoformat(),
                'activation_date': None,
                'role': role
            }

            # Save to Firebase
            ref.child(license_key).set(license_data)

            # Show success message
            result_text = f"License Key: {license_key}\nValid for: {days} days from activation\nRole: {role}"
            result_label.config(text=result_text)

            # Copy to clipboard
            app.clipboard_clear()
            app.clipboard_append(license_key)

            log_message(f"Generated new license: {license_key} (Role: {role}, Days: {days})")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number of days")
        except Exception as e:
            log_message(f"Error generating license: {str(e)}")
            messagebox.showerror("Error", f"Failed to generate license: {str(e)}")

    # Days input
    days_frame = ttk.Frame(license_frame)
    days_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(days_frame, text="Days Valid:").pack(side=tk.LEFT, padx=5)
    days_var = StringVar(value="30")
    ttk.Entry(days_frame, textvariable=days_var, width=10).pack(side=tk.LEFT, padx=5)

    # Role selection
    role_frame = ttk.Frame(license_frame)
    role_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(role_frame, text="Role:").pack(side=tk.LEFT, padx=5)
    role_var = StringVar(value="user")
    ttk.Combobox(role_frame, textvariable=role_var, values=["user", "admin"], state="readonly", width=10).pack(side=tk.LEFT, padx=5)

    # Generate button
    ttk.Button(license_frame, text="Generate License", command=generate_license, bootstyle="success").pack(pady=10)

    # Result display
    result_label = ttk.Label(license_frame, text="", wraplength=400)
    result_label.pack(pady=10)

    # List licenses button
    def list_licenses():
        try:
            ref = db.reference('licenses')
            licenses = ref.get()

            if not licenses:
                MessageDialog.show_info("Licenses", "No licenses found.")
                return

            license_window = ttk.Toplevel(admin_window)
            license_window.title("License List")
            license_window.geometry("600x400")

            # Create a treeview
            columns = ("key", "status", "role", "expiry", "device")
            tree = ttk.Treeview(license_window, columns=columns, show="headings")

            # Define headings
            tree.heading("key", text="License Key")
            tree.heading("status", text="Status")
            tree.heading("role", text="Role")
            tree.heading("expiry", text="Expiry Date")
            tree.heading("device", text="Device ID")

            # Define column widths
            tree.column("key", width=150)
            tree.column("status", width=80)
            tree.column("role", width=80)
            tree.column("expiry", width=150)
            tree.column("device", width=100)

            # Add a scrollbar
            scrollbar = ttk.Scrollbar(license_window, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscroll=scrollbar.set)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            tree.pack(expand=True, fill=tk.BOTH)

            # Add data to the treeview
            for key, data in licenses.items():
                status = "Active" if data.get('is_active', False) else "Inactive"
                role = data.get('role', 'user')
                expiry = data.get('expiry_date', 'N/A')
                device = data.get('device_id', 'Not bound')[:10] + '...' if data.get('device_id') else 'Not bound'

                tree.insert("", tk.END, values=(key, status, role, expiry, device))

        except Exception as e:
            log_message(f"Error listing licenses: {str(e)}")
            messagebox.showerror("Error", f"Failed to list licenses: {str(e)}")

    ttk.Button(admin_window, text="List All Licenses", command=list_licenses, bootstyle="info").pack(pady=10)

    # Close button
    ttk.Button(admin_window, text="Close", command=admin_window.destroy, bootstyle="danger").pack(pady=20)

admin_button = CustomButton(app, text="Admin Panel", command=show_admin_panel, bootstyle="warning")
# Admin button will be shown/hidden in update_license_status() based on role

# Log Area
log_area = scrolledtext.ScrolledText(
    app,
    wrap=tk.WORD,
    width=90,
    height=20,
    font=("Arial", 10),
    bg="#2E3440",
    fg="#D8DEE9",
    insertbackground="white",
)
log_area.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
# Log initial messages



keyboard.add_hotkey(params["start_shortcut"], toggle_auto_press)

keyboard.add_hotkey(params["stop_shortcut"], stop_macro)

# Register hide application hotkey
keyboard.add_hotkey(params["hide_shortcut"], toggle_app_visibility)

# Show/hide admin button based on role
if license_manager.is_admin():
    admin_button.pack(side=tk.RIGHT, padx=5, pady=5)
    log_message("Admin privileges detected")
else:
    # Hide admin button for regular users
    admin_button.pack_forget()

# Start periodic license checks
def check_license_periodically():
    if not license_manager.check_license_validity():
        MessageDialog.show_error("License Expired", "Your license has expired. Please renew to continue using the application.")
        app.destroy()
        sys.exit()
    # Schedule the next check
    app.after(params["license_check_interval"] * 1000, check_license_periodically)

# Function to explicitly refresh button styles
def refresh_button_styles():
    """Force refresh of button styles to ensure correct colors"""
    # Auto Key Presser buttons
    auto_press_button.configure(style='AutoPressStart.TButton')

    # Macro Recorder buttons
    record_button.configure(style='RecordStart.TButton')
    stop_macro_button.configure(bootstyle=DANGER)

    # AFK System buttons
    btn_start_loop.configure(style='LoopStart.TButton')
    btn_stop_loop.configure(style='LoopStop.TButton')

    # Apply the styles immediately
    app.update_idletasks()

# Start the license check and countdown
check_license_periodically()
update_license_status()

# Refresh button styles to ensure correct colors
app.after(100, refresh_button_styles)

# Set up protocol handler for window close button
app.protocol("WM_DELETE_WINDOW", exit_program)

# Show the main window now that all components are initialized
app.deiconify()

# Run main application
app.mainloop()
