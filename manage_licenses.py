import firebase_admin
from firebase_admin import credentials, db
import argparse
from datetime import datetime, timedelta
import secrets
import string
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, font
import webbrowser
import random
from functools import partial
import threading
import time

# Add parent directory to path to import firebase_config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from firebase_config import get_firebase_credentials, get_database_url

# Define application theme colors
COLORS = {
    "primary": "#3498db",    # Blue
    "secondary": "#2ecc71",  # Green
    "accent": "#e74c3c",     # Red
    "light": "#ecf0f1",      # Light Gray
    "dark": "#2c3e50",       # Dark Blue/Gray
    "warning": "#f39c12",    # Orange
    "success": "#27ae60",    # Dark Green
    "info": "#3498db",       # Blue
    "danger": "#c0392b"      # Dark Red
}

# Initialize Firebase
try:
    cred = get_firebase_credentials()
    firebase_admin.initialize_app(cred, {
        'databaseURL': get_database_url()
    })
    print("Firebase initialized successfully")
except Exception as e:
    print(f"Error initializing Firebase: {str(e)}")
    sys.exit(1)

def generate_license(days, role='user', license_type='permanent', user_info=None):
    """Generate a new license key

    Args:
        days (int): Number of days the license will be valid
        role (str, optional): Role for this license - 'user' or 'admin'. Defaults to 'user'.
        license_type (str, optional): Type of license - 'permanent' or 'temporary'. Defaults to 'permanent'.
        user_info (dict, optional): Information about the user. Defaults to None.

    Returns:
        str: The generated license key
    """
    # Generate a random license key (XXXX-XXXX-XXXX-XXXX format)
    chars = string.ascii_uppercase + string.digits
    segments = [''.join(secrets.choice(chars) for _ in range(4)) for _ in range(4)]
    license_key = '-'.join(segments)

    # Calculate expiry date
    expiry_date = (datetime.now() + timedelta(days=days)).isoformat()

    # Validate role
    if role not in ['user', 'admin']:
        print(f"Warning: Invalid role '{role}'. Using 'user' instead.")
        role = 'user'

    # Validate license type
    if license_type not in ['permanent', 'temporary']:
        print(f"Warning: Invalid license type '{license_type}'. Using 'permanent' instead.")
        license_type = 'permanent'

    # Create license data
    license_data = {
        'expiry_date': expiry_date,
        'is_active': True,
        'device_id': None,
        'created_date': datetime.now().isoformat(),
        'activation_date': None,
        'role': role,
        'license_type': license_type,
        'usage_count': 0,
        'max_usage': 1 if license_type == 'temporary' else None,
        'user_info': user_info or {},
        'device_attempts': [],  # List to track different device IDs that tried to use this license
        'attempt_count': 0,     # Counter for different device attempts
        'last_attempt_time': None,  # When the last attempt was made
        'blocked_until': None,   # When the license will be unblocked (if blocked)
        'is_connected': False,  # Whether the user is currently connected
        'last_connected': None,  # When the user last connected
        'last_disconnected': None  # When the user last disconnected
    }

    # Save to Firebase
    ref = db.reference('licenses')
    ref.child(license_key).set(license_data)

    print(f"Generated license key: {license_key}")
    print(f"Expires on: {expiry_date}")
    print(f"Role: {role}")
    print(f"License type: {license_type}")
    if user_info:
        print(f"User: {user_info.get('name', 'Unknown')}")
    return license_key

def list_licenses():
    """List all licenses and their status"""
    ref = db.reference('licenses')
    licenses = ref.get()

    if not licenses:
        print("No licenses found.")
        return

    print("\nLicense List:")
    print("-" * 80)
    for key, data in licenses.items():
        status = "Active" if data.get('is_active', False) else "Inactive"
        device = data.get('device_id', 'Not bound')
        role = data.get('role', 'user')
        license_type = data.get('license_type', 'permanent')
        usage_count = data.get('usage_count', 0)
        max_usage = data.get('max_usage', 'Unlimited')
        user_info = data.get('user_info', {})

        print(f"Key: {key}")
        print(f"Status: {status}")
        print(f"Role: {role}")
        print(f"Type: {license_type}")
        print(f"Expiry: {data.get('expiry_date', 'N/A')}")

        if license_type == 'temporary':
            print(f"Usage: {usage_count}/{max_usage}")

        print(f"Device ID: {device}")
        print(f"Created: {data.get('created_date', 'N/A')}")
        print(f"Activated: {data.get('activation_date', 'Not activated')}")

        if user_info:
            print(f"User: {user_info.get('name', 'Unknown')}")
            if 'email' in user_info:
                print(f"Email: {user_info.get('email')}")

        print("-" * 80)

    return licenses

def deactivate_license(license_key):
    """Deactivate a specific license key"""
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        print(f"License key {license_key} not found.")
        return

    ref.update({
        'is_active': False
    })
    print(f"License {license_key} has been deactivated.")

def delete_license(license_key):
    """Delete a license key from the database"""
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        print(f"License key {license_key} not found.")
        return False

    ref.delete()
    print(f"License {license_key} has been deleted.")
    return True

def track_device_attempt(license_key, device_id):
    """Track when a license is used on a different device

    Args:
        license_key (str): The license key
        device_id (str): The device ID attempting to use the license

    Returns:
        tuple: (is_allowed, message) - whether the attempt is allowed and a message
    """
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        return False, "License not found"

    # Check if license is blocked
    blocked_until = license_data.get('blocked_until')
    if blocked_until:
        try:
            block_time = datetime.fromisoformat(blocked_until)
            if block_time > datetime.now():
                # Still blocked
                remaining = (block_time - datetime.now()).total_seconds() / 3600  # hours
                return False, f"License is blocked for {remaining:.1f} more hours"
            else:
                # Block period is over, clear the block
                license_data['blocked_until'] = None
        except (ValueError, TypeError):
            # Invalid date format, clear the block
            license_data['blocked_until'] = None

    # Get the bound device ID
    bound_device_id = license_data.get('device_id')

    # If this is the bound device, allow it
    if bound_device_id == device_id:
        return True, "Device authorized"

    # If no device is bound yet, this is the first use
    if not bound_device_id:
        return True, "First device use"

    # This is a different device - track the attempt
    now = datetime.now().isoformat()
    device_attempts = license_data.get('device_attempts', [])
    attempt_count = license_data.get('attempt_count', 0)

    # Add this device to the attempts if it's not already there
    if device_id not in device_attempts:
        device_attempts.append(device_id)
        attempt_count += 1

    updates = {
        'device_attempts': device_attempts,
        'attempt_count': attempt_count,
        'last_attempt_time': now
    }

    # If we've reached 5 attempts, block the license for 1 day
    if attempt_count >= 5:
        block_until = (datetime.now() + timedelta(days=1)).isoformat()
        updates['blocked_until'] = block_until
        ref.update(updates)
        return False, "Too many different devices. License blocked for 24 hours."

    # Update the license data
    ref.update(updates)

    # Return unauthorized but don't block yet
    return False, f"Unauthorized device. {5 - attempt_count} attempts remaining before blocking."

def track_device_attempt(license_key, device_id):
    """Track when a license is used on a different device

    Args:
        license_key (str): The license key
        device_id (str): The device ID attempting to use the license

    Returns:
        tuple: (is_allowed, message) - whether the attempt is allowed and a message
    """
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        return False, "License not found"

    # Check if license is blocked
    blocked_until = license_data.get('blocked_until')
    if blocked_until:
        try:
            block_time = datetime.fromisoformat(blocked_until)
            if block_time > datetime.now():
                # Still blocked
                remaining = (block_time - datetime.now()).total_seconds() / 3600  # hours
                return False, f"License is blocked for {remaining:.1f} more hours"
            else:
                # Block period is over, clear the block
                license_data['blocked_until'] = None
        except (ValueError, TypeError):
            # Invalid date format, clear the block
            license_data['blocked_until'] = None

    # Get the bound device ID
    bound_device_id = license_data.get('device_id')

    # If this is the bound device, allow it
    if bound_device_id == device_id:
        return True, "Device authorized"

    # If no device is bound yet, this is the first use
    if not bound_device_id:
        return True, "First device use"

    # This is a different device - track the attempt
    now = datetime.now().isoformat()
    device_attempts = license_data.get('device_attempts', [])
    attempt_count = license_data.get('attempt_count', 0)

    # Add this device to the attempts if it's not already there
    if device_id not in device_attempts:
        device_attempts.append(device_id)
        attempt_count += 1

    updates = {
        'device_attempts': device_attempts,
        'attempt_count': attempt_count,
        'last_attempt_time': now
    }

    # If we've reached 5 attempts, block the license for 1 day
    if attempt_count >= 5:
        block_until = (datetime.now() + timedelta(days=1)).isoformat()
        updates['blocked_until'] = block_until
        ref.update(updates)
        return False, "Too many different devices. License blocked for 24 hours."

    # Update the license data
    ref.update(updates)

    # Return unauthorized but don't block yet
    return False, f"Unauthorized device. {5 - attempt_count} attempts remaining before blocking."

def reactivate_license(license_key, days=None):
    """Reactivate a license and optionally extend its validity"""
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        print(f"License key {license_key} not found.")
        return

    updates = {
        'is_active': True
    }

    if days:
        expiry_date = (datetime.now() + timedelta(days=days)).isoformat()
        updates['expiry_date'] = expiry_date
        print(f"Extended validity to: {expiry_date}")

    ref.update(updates)
    print(f"License {license_key} has been reactivated.")

def update_user_info(license_key, user_info):
    """Update user information for a license"""
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        print(f"License key {license_key} not found.")
        return False

    ref.update({
        'user_info': user_info
    })
    print(f"Updated user information for license {license_key}.")
    return True

def set_user_connected(license_key, connected=True):
    """Update the connection status of a user

    Args:
        license_key (str): The license key
        connected (bool): Whether the user is connected (True) or disconnected (False)

    Returns:
        bool: True if successful, False otherwise
    """
    ref = db.reference(f'licenses/{license_key}')
    license_data = ref.get()

    if not license_data:
        print(f"License key {license_key} not found.")
        return False

    now = datetime.now().isoformat()
    updates = {
        'is_connected': connected
    }

    if connected:
        updates['last_connected'] = now
    else:
        updates['last_disconnected'] = now

    ref.update(updates)

    status = "connected" if connected else "disconnected"
    print(f"User with license {license_key} is now {status}.")
    return True

def get_connected_users():
    """Get a list of all connected users

    Returns:
        dict: Dictionary of connected users with license keys as keys
    """
    ref = db.reference('licenses')
    licenses = ref.get() or {}

    connected_users = {}
    for key, data in licenses.items():
        if data.get('is_connected', False):
            connected_users[key] = data

    return connected_users


def extend_all_active_licenses(days=1):
    """Extend all active licenses by the specified number of days

    Args:
        days (int): Number of days to extend the licenses by

    Returns:
        int: Number of licenses extended
    """
    ref = db.reference('licenses')
    licenses = ref.get() or {}

    extended_count = 0
    for key, data in licenses.items():
        if data.get('is_active', False):
            try:
                # Get current expiry date
                current_expiry = datetime.fromisoformat(data.get('expiry_date', datetime.now().isoformat()))

                # Calculate new expiry date
                new_expiry = (current_expiry + timedelta(days=days)).isoformat()

                # Update license
                ref.child(key).update({
                    'expiry_date': new_expiry
                })

                extended_count += 1
                print(f"Extended license {key} to {new_expiry}")
            except (ValueError, TypeError) as e:
                print(f"Error extending license {key}: {str(e)}")
                continue

    print(f"Extended {extended_count} active licenses by {days} days")
    return extended_count

class BlockButton(tk.Button):
    """Custom button class with block color styling"""
    def __init__(self, master=None, **kwargs):
        self.style_name = kwargs.pop('style', 'primary')
        bg_color = self._get_bg_color(self.style_name)
        fg_color = self._get_fg_color(self.style_name)

        kwargs['bg'] = bg_color
        kwargs['fg'] = fg_color
        kwargs['relief'] = 'flat'
        kwargs['borderwidth'] = 0
        kwargs['highlightthickness'] = 0
        kwargs['font'] = ('Segoe UI', 10, 'bold')
        kwargs['activebackground'] = self._get_active_bg_color(self.style_name)
        kwargs['activeforeground'] = fg_color

        if 'width' not in kwargs:
            kwargs['width'] = 12

        if 'height' not in kwargs:
            kwargs['height'] = 1

        super().__init__(master, **kwargs)

        # Add hover effect
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)

    def _get_bg_color(self, style_name):
        colors = {
            'primary': COLORS["primary"],
            'success': COLORS["success"],
            'danger': COLORS["danger"],
            'warning': COLORS["warning"],
            'info': COLORS["info"],
            'dark': COLORS["dark"],
            'light': COLORS["light"]
        }
        return colors.get(style_name, COLORS["primary"])

    def _get_fg_color(self, style_name):
        if style_name == 'warning' or style_name == 'light':
            return 'black'
        return 'white'

    def _get_active_bg_color(self, style_name):
        # Slightly darker version of the background color for hover effect
        bg_color = self._get_bg_color(style_name)
        # Convert hex to RGB, darken, and convert back to hex
        r = int(bg_color[1:3], 16)
        g = int(bg_color[3:5], 16)
        b = int(bg_color[5:7], 16)

        # Darken by 15%
        r = max(0, r - 20)
        g = max(0, g - 20)
        b = max(0, b - 20)

        return f'#{r:02x}{g:02x}{b:02x}'

    def _on_enter(self, _):
        # Ignore the event parameter
        self['bg'] = self._get_active_bg_color(self.style_name)

    def _on_leave(self, _):
        # Ignore the event parameter
        self['bg'] = self._get_bg_color(self.style_name)

class CustomStyle(ttk.Style):
    """Custom style for the application"""
    def __init__(self):
        super().__init__()
        self.configure_styles()

    def configure_styles(self):
        # Configure TButton style
        self.configure('TButton', font=('Segoe UI', 10, 'bold'), padding=6)

        # Configure TLabel style
        self.configure('TLabel', font=('Segoe UI', 10))
        self.configure('Header.TLabel', font=('Segoe UI', 12, 'bold'))
        self.configure('Title.TLabel', font=('Segoe UI', 16, 'bold'))

        # Configure TFrame style
        self.configure('TFrame', background=COLORS["light"])
        self.configure('Card.TFrame', background="white", relief="raised", borderwidth=1)

        # Configure TLabelframe style
        self.configure('TLabelframe', background=COLORS["light"])
        self.configure('TLabelframe.Label', font=('Segoe UI', 11, 'bold'))

        # Configure TNotebook style
        self.configure('TNotebook', background=COLORS["light"])
        self.configure('TNotebook.Tab', font=('Segoe UI', 11), padding=[10, 5])

# Helper function to set window icon
def set_window_icon(window):
    """Set the LogoAdmin.ico icon for any window"""
    try:
        window.iconbitmap("LogoAdmin.ico")
    except tk.TclError:
        print("Warning: Could not load LogoAdmin.ico")

class LicenseManagerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("License Manager")
        self.root.geometry("1100x820")
        self.root.resizable(False, False)
        self.root.minsize(900, 600)

        # Set icon for main window
        set_window_icon(self.root)

        # Set custom styles
        self.style = CustomStyle()

        # Set background color
        self.root.configure(background=COLORS["light"])

        # Create header frame
        self.header_frame = ttk.Frame(root)
        self.header_frame.pack(fill=tk.X, padx=15, pady=(15, 5))

        # Add logo and title
        title_label = ttk.Label(self.header_frame, text="License Management System", style="Title.TLabel")
        title_label.pack(side=tk.LEFT, padx=10)

        # Add version info
        version_label = ttk.Label(self.header_frame, text="v1.0.0")
        version_label.pack(side=tk.RIGHT, padx=10)

        # Create main frame
        self.main_frame = ttk.Frame(root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # Create tabs with custom styling
        self.tab_control = ttk.Notebook(self.main_frame)

        self.licenses_tab = ttk.Frame(self.tab_control)
        self.generate_tab = ttk.Frame(self.tab_control)
        self.admin_tab = ttk.Frame(self.tab_control)
        self.connected_tab = ttk.Frame(self.tab_control)

        self.tab_control.add(self.licenses_tab, text="Licenses")
        self.tab_control.add(self.generate_tab, text="Generate License")
        self.tab_control.add(self.admin_tab, text="Administration")
        self.tab_control.add(self.connected_tab, text="Connected Users")

        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Create status bar
        self.status_frame = ttk.Frame(root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=5)

        self.status_label = ttk.Label(self.status_frame, text="Ready")
        self.status_label.pack(side=tk.LEFT)

        self.license_count_label = ttk.Label(self.status_frame, text="")
        self.license_count_label.pack(side=tk.RIGHT)

        # Setup tabs
        self.setup_licenses_tab()
        self.setup_generate_tab()
        self.setup_admin_tab()
        self.setup_connected_users_tab()

        # Load licenses
        self.load_licenses()

        # Start periodic refresh of connected users
        self.refresh_connected_users()
        self.root.after(30000, self.periodic_refresh_connected_users)  # Refresh every 30 seconds

        # Update status bar with license count
        self.update_status_bar()

    def update_status_bar(self):
        """Update the status bar with current information"""
        if hasattr(self, 'licenses') and self.licenses:
            active_count = sum(1 for data in self.licenses.values() if data.get('is_active', False))
            total_count = len(self.licenses)
            self.license_count_label.config(text=f"Active Licenses: {active_count}/{total_count}")
        else:
            self.license_count_label.config(text="No licenses found")

    def setup_licenses_tab(self):
        # Create main container
        main_container = ttk.Frame(self.licenses_tab)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Create search frame at the top with card style
        search_frame = ttk.LabelFrame(main_container, text="Search Licenses")
        search_frame.pack(fill=tk.X, padx=10, pady=10)

        # Create search fields with better layout
        search_fields_frame = ttk.Frame(search_frame)
        search_fields_frame.pack(fill=tk.X, padx=10, pady=10)

        # First row
        ttk.Label(search_fields_frame, text="License Key:", style="Header.TLabel").grid(row=0, column=0, padx=10, pady=10, sticky=tk.W)
        self.key_search_var = tk.StringVar()
        key_entry = ttk.Entry(search_fields_frame, textvariable=self.key_search_var, width=30, font=('Segoe UI', 10))
        key_entry.grid(row=0, column=1, padx=10, pady=10, sticky=tk.W)

        ttk.Label(search_fields_frame, text="Device ID:", style="Header.TLabel").grid(row=0, column=2, padx=10, pady=10, sticky=tk.W)
        self.device_search_var = tk.StringVar()
        device_entry = ttk.Entry(search_fields_frame, textvariable=self.device_search_var, width=30, font=('Segoe UI', 10))
        device_entry.grid(row=0, column=3, padx=10, pady=10, sticky=tk.W)

        # Second row
        ttk.Label(search_fields_frame, text="User:", style="Header.TLabel").grid(row=1, column=0, padx=10, pady=10, sticky=tk.W)
        self.user_search_var = tk.StringVar()
        user_entry = ttk.Entry(search_fields_frame, textvariable=self.user_search_var, width=30, font=('Segoe UI', 10))
        user_entry.grid(row=1, column=1, padx=10, pady=10, sticky=tk.W)

        # Create button frame for better alignment
        button_frame = ttk.Frame(search_fields_frame)
        button_frame.grid(row=1, column=2, columnspan=2, padx=10, pady=10, sticky=tk.E)

        # Create styled buttons with block color
        search_btn = BlockButton(button_frame, text="Search", command=self.search_licenses, style="primary")
        search_btn.pack(side=tk.RIGHT, padx=5)

        clear_btn = BlockButton(button_frame, text="Clear", command=self.clear_search, style="dark")
        clear_btn.pack(side=tk.RIGHT, padx=5)

        # Create content frame with better spacing
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create frame for license list with better styling
        list_frame = ttk.LabelFrame(content_frame, text="License List")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create license listbox with better styling
        self.license_listbox = tk.Listbox(
            list_frame,
            width=40,
            height=20,
            font=("Consolas", 11),
            bg="white",
            selectbackground=COLORS["primary"],
            selectforeground="white",
            activestyle="none",
            borderwidth=0,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.license_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add scrollbar with better styling
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.license_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 5), pady=5)
        self.license_listbox.config(yscrollcommand=scrollbar.set)

        # Bind selection event
        self.license_listbox.bind('<<ListboxSelect>>', self.on_license_select)

        # Create frame for license details with better styling
        details_frame = ttk.LabelFrame(content_frame, text="License Details")
        details_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create text widget for license details with better styling
        self.details_text = tk.Text(
            details_frame,
            width=40,
            height=20,
            wrap=tk.WORD,
            font=("Consolas", 11),
            bg="white",
            borderwidth=0,
            padx=10,
            pady=10,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.details_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.details_text.config(state=tk.DISABLED)

        # Create buttons frame with better styling
        buttons_frame = ttk.Frame(details_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=10)

        # Create styled buttons with block color
        self.activate_btn = BlockButton(
            buttons_frame,
            text="Activate",
            command=self.activate_license,
            style="success",
            width=10
        )
        self.activate_btn.pack(side=tk.LEFT, padx=5)

        self.deactivate_btn = BlockButton(
            buttons_frame,
            text="Deactivate",
            command=self.deactivate_license,
            style="danger",
            width=10
        )
        self.deactivate_btn.pack(side=tk.LEFT, padx=5)

        self.extend_btn = BlockButton(
            buttons_frame,
            text="Extend",
            command=self.extend_license,
            style="primary",
            width=10
        )
        self.extend_btn.pack(side=tk.LEFT, padx=5)

        self.user_info_btn = BlockButton(
            buttons_frame,
            text="Edit User",
            command=self.edit_user_info,
            style="info",
            width=10
        )
        self.user_info_btn.pack(side=tk.LEFT, padx=5)

        self.delete_btn = BlockButton(
            buttons_frame,
            text="Delete",
            command=self.delete_license,
            style="danger",
            width=10
        )
        self.delete_btn.pack(side=tk.LEFT, padx=5)

        self.refresh_btn = BlockButton(
            buttons_frame,
            text="Refresh",
            command=self.load_licenses,
            style="dark",
            width=10
        )
        self.refresh_btn.pack(side=tk.RIGHT, padx=5)

    def setup_generate_tab(self):
        # Create main container with better styling
        main_container = ttk.Frame(self.generate_tab)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create left panel for form
        left_panel = ttk.Frame(main_container)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10)

        # Create form frame with better styling
        form_frame = ttk.LabelFrame(left_panel, text="Generate New License", padding=15)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Create form with better styling and layout
        form_grid = ttk.Frame(form_frame)
        form_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Days validity with better styling
        ttk.Label(form_grid, text="Validity (days):", style="Header.TLabel").grid(row=0, column=0, sticky=tk.W, pady=15, padx=10)
        self.days_var = tk.StringVar(value="30")
        days_entry = ttk.Entry(form_grid, textvariable=self.days_var, width=15, font=('Segoe UI', 10))
        days_entry.grid(row=0, column=1, sticky=tk.W, pady=15, padx=10)

        # Role with better styling
        ttk.Label(form_grid, text="Role:", style="Header.TLabel").grid(row=1, column=0, sticky=tk.W, pady=15, padx=10)
        self.role_var = tk.StringVar(value="user")
        role_combo = ttk.Combobox(form_grid, textvariable=self.role_var, width=15, font=('Segoe UI', 10))
        role_combo['values'] = ('user', 'admin')
        role_combo.grid(row=1, column=1, sticky=tk.W, pady=15, padx=10)

        # License type with better styling
        ttk.Label(form_grid, text="License Type:", style="Header.TLabel").grid(row=2, column=0, sticky=tk.W, pady=15, padx=10)
        self.type_var = tk.StringVar(value="permanent")
        type_combo = ttk.Combobox(form_grid, textvariable=self.type_var, width=15, font=('Segoe UI', 10))
        type_combo['values'] = ('permanent', 'temporary')
        type_combo.grid(row=2, column=1, sticky=tk.W, pady=15, padx=10)

        # User info with better styling
        user_frame = ttk.LabelFrame(form_grid, text="User Information", padding=10)
        user_frame.grid(row=3, column=0, columnspan=2, sticky=tk.EW, pady=15, padx=10)

        ttk.Label(user_frame, text="Name:").grid(row=0, column=0, sticky=tk.W, pady=10, padx=5)
        self.user_name_var = tk.StringVar()
        name_entry = ttk.Entry(user_frame, textvariable=self.user_name_var, width=30, font=('Segoe UI', 10))
        name_entry.grid(row=0, column=1, sticky=tk.W, pady=10, padx=5)

        ttk.Label(user_frame, text="Email:").grid(row=1, column=0, sticky=tk.W, pady=10, padx=5)
        self.user_email_var = tk.StringVar()
        email_entry = ttk.Entry(user_frame, textvariable=self.user_email_var, width=30, font=('Segoe UI', 10))
        email_entry.grid(row=1, column=1, sticky=tk.W, pady=10, padx=5)

        # Buttons frame with better styling
        buttons_frame = ttk.Frame(form_grid)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

        # Create 3-day license button with block color (updated text to reflect permanent type)
        temp_btn = BlockButton(
            buttons_frame,
            text="Create 3-Day License",
            command=self.create_3day_license,
            style="warning",
            width=25
        )
        temp_btn.pack(side=tk.LEFT, padx=10)

        # Generate button with block color
        gen_btn = BlockButton(
            buttons_frame,
            text="Generate License",
            command=self.generate_license,
            style="primary",
            width=15
        )
        gen_btn.pack(side=tk.LEFT, padx=10)

        # Create right panel for result
        right_panel = ttk.Frame(main_container)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10)

        # Result frame with better styling
        result_frame = ttk.LabelFrame(right_panel, text="Generated License", padding=15)
        result_frame.pack(fill=tk.BOTH, expand=True)

        # Result text with better styling
        self.result_text = tk.Text(
            result_frame,
            height=15,
            width=40,
            wrap=tk.WORD,
            font=("Consolas", 11),
            bg="white",
            borderwidth=0,
            padx=10,
            pady=10,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add copy button with block color
        copy_btn = BlockButton(
            result_frame,
            text="Copy to Clipboard",
            command=self.copy_result_to_clipboard,
            style="info",
            width=15
        )
        copy_btn.pack(side=tk.RIGHT, padx=10, pady=10)

    def copy_result_to_clipboard(self):
        """Copy the result text to clipboard"""
        result = self.result_text.get(1.0, tk.END).strip()
        if result:
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            self.status_label.config(text="License key copied to clipboard")
            # Reset status after 3 seconds
            self.root.after(3000, lambda: self.status_label.config(text="Ready"))

    def setup_admin_tab(self):
        """Setup the administration tab with advanced features"""
        # Create main container with better styling
        main_container = ttk.Frame(self.admin_tab)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create top section with statistics and quick actions
        top_section = ttk.Frame(main_container)
        top_section.pack(fill=tk.X, pady=10)

        # Create statistics frame with better styling
        stats_frame = ttk.LabelFrame(top_section, text="License Statistics", padding=15)
        stats_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        # Create statistics text with better styling
        self.stats_text = tk.Text(
            stats_frame,
            height=6,
            width=40,
            wrap=tk.WORD,
            font=("Consolas", 11),
            bg="white",
            borderwidth=0,
            padx=10,
            pady=10,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create quick actions frame
        quick_actions = ttk.LabelFrame(top_section, text="Quick Actions", padding=15)
        quick_actions.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)

        # Create quick action buttons with better styling
        quick_btn_frame = ttk.Frame(quick_actions)
        quick_btn_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        BlockButton(
            quick_btn_frame,
            text="Revoke Expired Licenses",
            command=self.revoke_expired_licenses,
            style="danger",
            width=20
        ).pack(fill=tk.X, pady=5)

        BlockButton(
            quick_btn_frame,
            text="Export Licenses",
            command=self.export_licenses,
            style="primary",
            width=20
        ).pack(fill=tk.X, pady=5)

        BlockButton(
            quick_btn_frame,
            text="Refresh Statistics",
            command=self.update_statistics,
            style="dark",
            width=20
        ).pack(fill=tk.X, pady=5)

        BlockButton(
            quick_btn_frame,
            text="Extend All Active Licenses (+1 day)",
            command=self.extend_all_active_licenses,
            style="success",
            width=25
        ).pack(fill=tk.X, pady=5)

        # Create middle section with administrative actions
        middle_section = ttk.LabelFrame(main_container, text="Administrative Actions", padding=15)
        middle_section.pack(fill=tk.X, pady=10)

        # Create action buttons grid with better styling
        actions_grid = ttk.Frame(middle_section)
        actions_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Row 1 of buttons
        action_btn1 = BlockButton(
            actions_grid,
            text="Bulk Generate Licenses",
            command=self.bulk_generate_licenses,
            style="success"
        )
        action_btn1.grid(row=0, column=0, padx=15, pady=15, sticky=tk.EW)

        action_btn2 = BlockButton(
            actions_grid,
            text="Export Licenses to CSV",
            command=self.export_licenses,
            style="info"
        )
        action_btn2.grid(row=0, column=1, padx=15, pady=15, sticky=tk.EW)

        action_btn3 = BlockButton(
            actions_grid,
            text="Revoke All Expired Licenses",
            command=self.revoke_expired_licenses,
            style="danger"
        )
        action_btn3.grid(row=0, column=2, padx=15, pady=15, sticky=tk.EW)

        # Row 2 of buttons
        action_btn4 = BlockButton(
            actions_grid,
            text="View User Activity",
            command=self.view_user_activity,
            style="primary"
        )
        action_btn4.grid(row=1, column=0, padx=15, pady=15, sticky=tk.EW)

        action_btn5 = BlockButton(
            actions_grid,
            text="Backup Database",
            command=self.backup_database,
            style="info"
        )
        action_btn5.grid(row=1, column=1, padx=15, pady=15, sticky=tk.EW)

        action_btn6 = BlockButton(
            actions_grid,
            text="System Settings",
            command=self.system_settings,
            style="dark"
        )
        action_btn6.grid(row=1, column=2, padx=15, pady=15, sticky=tk.EW)

        # Configure grid columns to be equal width
        actions_grid.columnconfigure(0, weight=1)
        actions_grid.columnconfigure(1, weight=1)
        actions_grid.columnconfigure(2, weight=1)

        # Create bottom section with activity log
        bottom_section = ttk.LabelFrame(main_container, text="Activity Log", padding=15)
        bottom_section.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create log container
        log_container = ttk.Frame(bottom_section)
        log_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create log text widget with better styling
        self.log_text = tk.Text(
            log_container,
            height=10,
            width=80,
            wrap=tk.WORD,
            font=("Consolas", 11),
            bg="white",
            borderwidth=0,
            padx=10,
            pady=10,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add scrollbar to log with better styling
        log_scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=log_scrollbar.set)

        # Create log control buttons
        log_buttons = ttk.Frame(bottom_section)
        log_buttons.pack(fill=tk.X, pady=5)

        BlockButton(
            log_buttons,
            text="Clear Log",
            command=lambda: self.log_text.delete(1.0, tk.END),
            style="danger",
            width=10
        ).pack(side=tk.RIGHT, padx=5)

        BlockButton(
            log_buttons,
            text="Save Log",
            command=self.save_log,
            style="primary",
            width=10
        ).pack(side=tk.RIGHT, padx=5)

        # Initialize log with better formatting
        self.log_text.insert(tk.END, "✓ Administration panel initialized\n")
        self.log_text.insert(tk.END, f"✓ Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        self.log_text.insert(tk.END, "✓ Ready for administrative actions\n")

        # Update statistics
        self.update_statistics()

    def save_log(self):
        """Save the activity log to a file"""
        try:
            filename = f"license_activity_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, "w") as f:
                f.write(self.log_text.get(1.0, tk.END))

            self.log_text.insert(tk.END, f"✓ Log saved to {filename}\n")
            self.status_label.config(text=f"Log saved to {filename}")
            # Reset status after 3 seconds
            self.root.after(3000, lambda: self.status_label.config(text="Ready"))
        except Exception as e:
            self.log_text.insert(tk.END, f"✗ Error saving log: {str(e)}\n")

    def update_statistics(self):
        """Update license statistics in the admin panel"""
        if not hasattr(self, 'licenses') or not self.licenses:
            return

        # Clear stats text
        self.stats_text.delete(1.0, tk.END)

        # Count licenses by type and status
        total_licenses = len(self.licenses)
        active_licenses = sum(1 for data in self.licenses.values() if data.get('is_active', False))
        inactive_licenses = total_licenses - active_licenses

        permanent_licenses = sum(1 for data in self.licenses.values()
                               if data.get('license_type', 'permanent') == 'permanent')
        temporary_licenses = sum(1 for data in self.licenses.values()
                               if data.get('license_type', 'permanent') == 'temporary')

        admin_licenses = sum(1 for data in self.licenses.values()
                           if data.get('role', 'user') == 'admin')
        user_licenses = sum(1 for data in self.licenses.values()
                          if data.get('role', 'user') == 'user')

        bound_licenses = sum(1 for data in self.licenses.values()
                           if data.get('device_id'))



        # Add statistics to text widget
        stats = f"Total Licenses: {total_licenses}\n"
        stats += f"Active: {active_licenses} | Inactive: {inactive_licenses}\n"
        stats += f"Permanent: {permanent_licenses} | Temporary: {temporary_licenses}\n"
        stats += f"Admin: {admin_licenses} | User: {user_licenses}\n"
        stats += f"Bound to devices: {bound_licenses} | Unbound: {total_licenses - bound_licenses}\n"

        self.stats_text.insert(tk.END, stats)

    def bulk_generate_licenses(self):
        """Generate multiple licenses at once"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Bulk Generate Licenses")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Set icon for dialog
        set_window_icon(dialog)

        # Create form
        ttk.Label(dialog, text="Number of licenses:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        count_var = tk.StringVar(value="10")
        ttk.Entry(dialog, textvariable=count_var, width=10).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(dialog, text="Validity (days):").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        days_var = tk.StringVar(value="30")
        ttk.Entry(dialog, textvariable=days_var, width=10).grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(dialog, text="Role:").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        role_var = tk.StringVar(value="user")
        role_combo = ttk.Combobox(dialog, textvariable=role_var, width=10)
        role_combo['values'] = ('user', 'admin')
        role_combo.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)

        ttk.Label(dialog, text="License Type:").grid(row=3, column=0, padx=5, pady=5, sticky=tk.W)
        type_var = tk.StringVar(value="permanent")
        type_combo = ttk.Combobox(dialog, textvariable=type_var, width=10)
        type_combo['values'] = ('permanent', 'temporary')
        type_combo.grid(row=3, column=1, padx=5, pady=5, sticky=tk.W)

        # Result text
        result_text = tk.Text(dialog, height=8, width=40)
        result_text.grid(row=4, column=0, columnspan=2, padx=5, pady=5)

        # Generate function
        def do_generate():
            try:
                count = int(count_var.get())
                days = int(days_var.get())
                role = role_var.get()
                license_type = type_var.get()

                if count <= 0 or days <= 0:
                    messagebox.showerror("Error", "Count and days must be positive numbers")
                    return

                # Generate licenses
                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"Generating {count} licenses...\n\n")

                generated_keys = []
                for _ in range(count):  # Use _ for unused loop variable
                    key = generate_license(days, role, license_type)
                    generated_keys.append(key)
                    result_text.insert(tk.END, f"{key}\n")

                # Log to admin panel
                self.log_text.insert(tk.END, f"Generated {count} {license_type} licenses with {days} days validity.\n")

                # Refresh licenses
                self.load_licenses()
                self.update_statistics()

            except ValueError:
                messagebox.showerror("Error", "Please enter valid numbers")

        # Buttons with block color
        BlockButton(
            dialog,
            text="Generate",
            command=do_generate,
            style="success",
            width=10
        ).grid(row=5, column=1, padx=5, pady=5, sticky=tk.E)

        BlockButton(
            dialog,
            text="Close",
            command=dialog.destroy,
            style="danger",
            width=10
        ).grid(row=5, column=0, padx=5, pady=5, sticky=tk.W)

    def export_licenses(self):
        """Export licenses to CSV file"""
        if not hasattr(self, 'licenses') or not self.licenses:
            messagebox.showinfo("Info", "No licenses to export")
            return

        try:
            filename = "licenses_export_" + datetime.now().strftime("%Y%m%d_%H%M%S") + ".csv"

            with open(filename, "w", newline='') as f:
                f.write("License Key,Status,Role,Type,Expiry Date,Device ID,Created Date,Activation Date,User Name,User Email\n")

                for key, data in self.licenses.items():
                    status = "Active" if data.get('is_active', False) else "Inactive"
                    role = data.get('role', 'user')
                    license_type = data.get('license_type', 'permanent')
                    expiry = data.get('expiry_date', 'N/A')
                    device = data.get('device_id', '')
                    created = data.get('created_date', 'N/A')
                    activated = data.get('activation_date', '')

                    user_info = data.get('user_info', {})
                    user_name = user_info.get('name', '')
                    user_email = user_info.get('email', '')

                    f.write(f"{key},{status},{role},{license_type},{expiry},{device},{created},{activated},{user_name},{user_email}\n")

            self.log_text.insert(tk.END, f"Exported {len(self.licenses)} licenses to {filename}\n")
            messagebox.showinfo("Success", f"Exported {len(self.licenses)} licenses to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to export licenses: {str(e)}")

    def revoke_expired_licenses(self):
        """Revoke all expired licenses"""
        if not hasattr(self, 'licenses') or not self.licenses:
            messagebox.showinfo("Info", "No licenses to revoke")
            return

        # Confirm
        if not messagebox.askyesno("Confirm", "Are you sure you want to revoke all expired licenses?"):
            return

        try:
            now = datetime.now()
            revoked_count = 0

            for key, data in self.licenses.items():
                try:
                    expiry = datetime.fromisoformat(data.get('expiry_date', now.isoformat()))
                    if expiry < now and data.get('is_active', False):
                        deactivate_license(key)
                        revoked_count += 1
                except (ValueError, TypeError):
                    continue

            self.log_text.insert(tk.END, f"Revoked {revoked_count} expired licenses\n")
            messagebox.showinfo("Success", f"Revoked {revoked_count} expired licenses")

            # Refresh licenses
            self.load_licenses()
            self.update_statistics()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to revoke licenses: {str(e)}")

    def view_user_activity(self):
        """View user activity"""
        # Switch to the connected users tab
        self.tab_control.select(self.connected_tab)

    def setup_connected_users_tab(self):
        """Setup the connected users tab"""
        # Create main container with better styling
        main_container = ttk.Frame(self.connected_tab)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create top section with controls
        top_section = ttk.Frame(main_container)
        top_section.pack(fill=tk.X, pady=10)

        # Create refresh button
        refresh_btn = BlockButton(
            top_section,
            text="Refresh Connected Users",
            command=self.refresh_connected_users,
            style="primary",
            width=20
        )
        refresh_btn.pack(side=tk.LEFT, padx=10)

        # Create status label
        self.connected_status_label = ttk.Label(top_section, text="Last updated: Never")
        self.connected_status_label.pack(side=tk.RIGHT, padx=10)

        # Create action buttons
        action_frame = ttk.Frame(main_container)
        action_frame.pack(fill=tk.X, pady=5)

        # Add disconnect button
        disconnect_btn = BlockButton(
            action_frame,
            text="Disconnect Selected User",
            command=self.disconnect_user,
            style="danger",
            width=20
        )
        disconnect_btn.pack(side=tk.LEFT, padx=10)

        # Add message button
        message_btn = BlockButton(
            action_frame,
            text="Send Message",
            command=self.send_message_to_user,
            style="primary",
            width=15
        )
        message_btn.pack(side=tk.LEFT, padx=10)

        # Create connected users list frame
        list_frame = ttk.LabelFrame(main_container, text="Connected Users")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create connected users listbox
        self.connected_users_listbox = tk.Listbox(
            list_frame,
            font=("Consolas", 11),
            bg="white",
            selectbackground=COLORS["primary"],
            selectforeground="white",
            activestyle="none",
            borderwidth=0,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.connected_users_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.connected_users_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 5), pady=5)
        self.connected_users_listbox.config(yscrollcommand=scrollbar.set)

        # Bind selection event
        self.connected_users_listbox.bind('<<ListboxSelect>>', self.on_connected_user_select)

        # Create details frame
        details_frame = ttk.LabelFrame(main_container, text="User Details")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create details text
        self.connected_details_text = tk.Text(
            details_frame,
            height=10,
            wrap=tk.WORD,
            font=("Consolas", 11),
            bg="white",
            borderwidth=0,
            padx=10,
            pady=10,
            highlightthickness=1,
            highlightcolor=COLORS["primary"]
        )
        self.connected_details_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.connected_details_text.config(state=tk.DISABLED)

    def refresh_connected_users(self):
        """Refresh the list of connected users"""
        # Get connected users
        connected_users = get_connected_users()

        # Clear listbox
        self.connected_users_listbox.delete(0, tk.END)

        if not connected_users:
            self.connected_users_listbox.insert(tk.END, "No connected users")
            self.connected_status_label.config(text=f"Last updated: {datetime.now().strftime('%H:%M:%S')}")
            return

        # Add connected users to listbox
        for key, data in connected_users.items():
            user_info = data.get('user_info', {})
            user_name = user_info.get('name', 'Unknown')

            # Get connection time
            last_connected = data.get('last_connected', 'Unknown')
            try:
                connected_time = datetime.fromisoformat(last_connected)
                time_ago = (datetime.now() - connected_time).total_seconds() / 60  # minutes

                if time_ago < 60:
                    time_str = f"{int(time_ago)}m ago"
                else:
                    time_str = f"{int(time_ago/60)}h {int(time_ago%60)}m ago"
            except (ValueError, TypeError):
                time_str = "Unknown"

            # Format display string
            display = f"{user_name} ({key[:8]}...) - Connected: {time_str}"

            self.connected_users_listbox.insert(tk.END, display)

            # Store the full key for later use
            self.connected_users_listbox.itemconfig(tk.END, {'values': key})

        # Update status label
        self.connected_status_label.config(text=f"Last updated: {datetime.now().strftime('%H:%M:%S')} - {len(connected_users)} users online")

    def periodic_refresh_connected_users(self):
        """Periodically refresh the connected users list"""
        self.refresh_connected_users()
        # Schedule next refresh
        self.root.after(30000, self.periodic_refresh_connected_users)  # Refresh every 30 seconds

    def disconnect_user(self):
        """Disconnect the selected user"""
        # Get selected index
        selection = self.connected_users_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user to disconnect")
            return

        # Get the license key from the item's values
        index = selection[0]
        try:
            # Check if this is the "No connected users" message
            if self.connected_users_listbox.get(index) == "No connected users":
                return

            # Get the license key from the item's values
            key = self.connected_users_listbox.itemconfig(index, 'values')[-1]

            # Confirm disconnection
            if not messagebox.askyesno("Confirm Disconnection", f"Are you sure you want to disconnect this user?\n\nThis will mark them as disconnected in the database, but won't actually force close their application."):
                return

            # Disconnect the user
            set_user_connected(key, connected=False)

            # Log the action
            self.log_text.insert(tk.END, f"Disconnected user with license {key}\n")

            # Refresh the connected users list
            self.refresh_connected_users()

            messagebox.showinfo("Success", "User has been marked as disconnected")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to disconnect user: {str(e)}")

    def send_message_to_user(self):
        """Send a message to the selected user"""
        # Get selected index
        selection = self.connected_users_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a user to message")
            return

        # Get the license key from the item's values
        index = selection[0]
        try:
            # Check if this is the "No connected users" message
            if self.connected_users_listbox.get(index) == "No connected users":
                return

            # Get the license key from the item's values
            key = self.connected_users_listbox.itemconfig(index, 'values')[-1]

            # Get the message from the user
            message = simpledialog.askstring("Send Message", "Enter the message to send to the user:", parent=self.root)
            if not message:
                return

            # This is a placeholder for future functionality
            # In a real implementation, you would send the message to the user's application
            messagebox.showinfo("Feature Not Implemented", "The messaging feature is not yet implemented")

            # Log the action
            self.log_text.insert(tk.END, f"Attempted to send message to user with license {key}: {message}\n")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to send message: {str(e)}")

    def on_connected_user_select(self, _):
        """Handle selection of a connected user"""
        # Get selected index
        selection = self.connected_users_listbox.curselection()
        if not selection:
            return

        # Get the license key from the item's values
        index = selection[0]
        try:
            # Check if this is the "No connected users" message
            if self.connected_users_listbox.get(index) == "No connected users":
                return

            # Get the license key from the item's values
            key = self.connected_users_listbox.itemconfig(index, 'values')[-1]

            # Store the selected key
            self.selected_connected_key = key

            # Get license data
            license_data = self.licenses.get(key, {})

            # Update details text
            self.connected_details_text.config(state=tk.NORMAL)
            self.connected_details_text.delete(1.0, tk.END)

            # Format details
            details = f"📋 Connected User Information\n"
            details += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

            # User info
            user_info = license_data.get('user_info', {})
            user_name = user_info.get('name', 'Unknown')
            user_email = user_info.get('email', 'N/A')

            details += f"👤 User: {user_name}\n"
            details += f"📧 Email: {user_email}\n"
            details += f"🔑 License: {key}\n"

            # Connection info
            last_connected = license_data.get('last_connected', 'N/A')
            try:
                connected_time = datetime.fromisoformat(last_connected)
                formatted_time = connected_time.strftime("%Y-%m-%d %H:%M:%S")

                # Calculate time connected
                time_connected = (datetime.now() - connected_time).total_seconds()
                hours = int(time_connected // 3600)
                minutes = int((time_connected % 3600) // 60)
                seconds = int(time_connected % 60)

                details += f"🔌 Connected since: {formatted_time}\n"
                details += f"⏱️ Time connected: {hours}h {minutes}m {seconds}s\n"
            except (ValueError, TypeError):
                details += f"🔌 Connected since: {last_connected}\n"

            # Last disconnection info if available
            last_disconnected = license_data.get('last_disconnected', None)
            if last_disconnected:
                try:
                    disconnected_time = datetime.fromisoformat(last_disconnected)
                    formatted_time = disconnected_time.strftime("%Y-%m-%d %H:%M:%S")
                    details += f"🔌 Last disconnected: {formatted_time}\n"
                except (ValueError, TypeError):
                    details += f"🔌 Last disconnected: {last_disconnected}\n"

            # Device info
            device_id = license_data.get('device_id', 'Not bound')
            if device_id:
                short_device = device_id[:15] + "..." if len(device_id) > 18 else device_id
                details += f"💻 Device ID: {short_device}\n"

            # License info
            expiry_date = license_data.get('expiry_date', 'N/A')
            try:
                expiry = datetime.fromisoformat(expiry_date)
                formatted_expiry = expiry.strftime("%Y-%m-%d")
                details += f"📅 License expires: {formatted_expiry}\n"
            except (ValueError, TypeError):
                details += f"📅 License expires: {expiry_date}\n"

            # Insert details
            self.connected_details_text.insert(tk.END, details)
            self.connected_details_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load user details: {str(e)}")

    def extend_all_active_licenses(self):
        """Extend all active licenses by 1 day"""
        # Confirm action
        if not messagebox.askyesno("Confirm", "Are you sure you want to extend ALL active licenses by 1 day?"):
            return

        # Show progress dialog
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("Extending Licenses")
        progress_dialog.geometry("300x100")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()

        # Set icon for dialog
        set_window_icon(progress_dialog)

        # Add progress message
        ttk.Label(progress_dialog, text="Extending all active licenses...", padding=10).pack()

        # Add progress bar
        progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        progress_bar.start()

        # Update UI
        progress_dialog.update()

        # Extend licenses in a separate thread to keep UI responsive
        def extend_licenses_thread():
            try:
                # Extend all active licenses
                extended_count = extend_all_active_licenses(days=1)

                # Close progress dialog
                progress_dialog.destroy()

                # Show success message
                messagebox.showinfo("Success", f"Extended {extended_count} active licenses by 1 day")

                # Refresh licenses
                self.load_licenses()
                self.update_statistics()

            except Exception as e:
                # Close progress dialog
                progress_dialog.destroy()

                # Show error message
                messagebox.showerror("Error", f"Failed to extend licenses: {str(e)}")

        # Start thread
        threading.Thread(target=extend_licenses_thread, daemon=True).start()

    def backup_database(self):
        """Backup the license database"""
        messagebox.showinfo("Info", "Database backup functionality is not implemented yet")

    def system_settings(self):
        """System settings dialog"""
        messagebox.showinfo("Info", "System settings functionality is not implemented yet")

    def search_licenses(self):
        """Search licenses based on search criteria"""
        if not hasattr(self, 'licenses') or not self.licenses:
            return

        # Get search criteria
        key_search = self.key_search_var.get().strip().upper()
        device_search = self.device_search_var.get().strip().lower()
        user_search = self.user_search_var.get().strip().lower()

        # Clear listbox
        self.license_listbox.delete(0, tk.END)

        # Filter licenses
        found = False
        for key, data in self.licenses.items():
            # Check if license matches search criteria
            key_match = not key_search or key_search in key

            device_id = data.get('device_id', '').lower()
            device_match = not device_search or device_search in device_id

            user_info = data.get('user_info', {})
            user_name = user_info.get('name', '').lower()
            user_email = user_info.get('email', '').lower()
            user_match = not user_search or user_search in user_name or user_search in user_email

            # If all criteria match, add to listbox
            if key_match and device_match and user_match:
                self.license_listbox.insert(tk.END, key)
                found = True

        if not found:
            self.license_listbox.insert(tk.END, "No matching licenses found")

    def clear_search(self):
        """Clear search fields and reload all licenses"""
        self.key_search_var.set("")
        self.device_search_var.set("")
        self.user_search_var.set("")
        self.load_licenses()

    def load_licenses(self):
        """Load all licenses into the listbox"""
        # Clear listbox
        self.license_listbox.delete(0, tk.END)

        # Get licenses
        self.licenses = list_licenses()

        if not self.licenses:
            self.license_listbox.insert(tk.END, "No licenses found")
            return

        # Add licenses to listbox with color coding for unauthorized attempts
        for key, data in self.licenses.items():
            self.license_listbox.insert(tk.END, key)

            # Check if this license has been used on multiple devices
            attempt_count = data.get('attempt_count', 0)
            blocked_until = data.get('blocked_until')

            # Color the license red if it has unauthorized attempts or is blocked
            if attempt_count > 0 or blocked_until:
                # Set the text color to red for this item
                self.license_listbox.itemconfig(tk.END, {'fg': COLORS["danger"]})

        # Update statistics if admin tab exists
        if hasattr(self, 'stats_text'):
            self.update_statistics()

    def on_license_select(self, _):
        # Get selected license (ignoring the event parameter)
        selection = self.license_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        key = self.license_listbox.get(index)

        if key == "No licenses found" or key == "No matching licenses found":
            return

        # Get license data
        data = self.licenses.get(key, {})

        # Update details text
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)

        status = "Active" if data.get('is_active', False) else "Inactive"
        device = data.get('device_id', 'Not bound')
        role = data.get('role', 'user')
        license_type = data.get('license_type', 'permanent')
        usage_count = data.get('usage_count', 0)
        max_usage = data.get('max_usage', 'Unlimited')
        user_info = data.get('user_info', {})

        # Format details with better styling
        details = f"📋 License Information\n"
        details += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
        details += f"🔑 Key: {key}\n\n"

        # Status with emoji
        if status == "Active":
            details += f"✅ Status: {status}\n"
        else:
            details += f"❌ Status: {status}\n"

        # Check if license is blocked
        blocked_until = data.get('blocked_until')
        if blocked_until:
            try:
                block_time = datetime.fromisoformat(blocked_until)
                if block_time > datetime.now():
                    # Still blocked - show in red (will apply tag later)
                    remaining_hours = (block_time - datetime.now()).total_seconds() / 3600
                    details += f"🚫 BLOCKED: {remaining_hours:.1f} hours remaining\n"
            except (ValueError, TypeError):
                pass

        # Role with emoji
        if role == "admin":
            details += f"👑 Role: {role}\n"
        else:
            details += f"👤 Role: {role}\n"

        # License type with emoji
        if license_type == "permanent":
            details += f"🔒 Type: {license_type}\n"
        else:
            details += f"⏱️ Type: {license_type}\n"

        # Format expiry date more nicely
        try:
            expiry_date = datetime.fromisoformat(data.get('expiry_date', ''))
            formatted_expiry = expiry_date.strftime("%B %d, %Y")
            details += f"📅 Expiry: {formatted_expiry}\n"
        except (ValueError, TypeError):
            details += f"📅 Expiry: {data.get('expiry_date', 'N/A')}\n"

        # Usage for temporary licenses
        if license_type == 'temporary':
            details += f"🔄 Usage: {usage_count}/{max_usage}\n"

        # Device ID (shortened for readability)
        if device != 'Not bound':
            short_device = device[:15] + "..." if len(device) > 18 else device
            details += f"💻 Device ID: {short_device}\n"
        else:
            details += f"💻 Device ID: Not bound\n"

        # Show unauthorized device attempts
        attempt_count = data.get('attempt_count', 0)
        if attempt_count > 0:
            details += f"\n⚠️ Unauthorized device attempts: {attempt_count}/5\n"

            # Show the device IDs that attempted to use this license
            device_attempts = data.get('device_attempts', [])
            if device_attempts:
                details += "Devices that attempted to use this license:\n"
                for i, dev_id in enumerate(device_attempts):
                    short_dev = dev_id[:15] + "..." if len(dev_id) > 18 else dev_id
                    details += f"  {i+1}. {short_dev}\n"

        # Format dates more nicely
        try:
            created_date = datetime.fromisoformat(data.get('created_date', ''))
            formatted_created = created_date.strftime("%B %d, %Y %H:%M")
            details += f"🆕 Created: {formatted_created}\n"
        except (ValueError, TypeError):
            details += f"🆕 Created: {data.get('created_date', 'N/A')}\n"

        try:
            if data.get('activation_date'):
                activation_date = datetime.fromisoformat(data.get('activation_date', ''))
                formatted_activation = activation_date.strftime("%B %d, %Y %H:%M")
                details += f"🚀 Activated: {formatted_activation}\n"
            else:
                details += f"🚀 Activated: Not activated\n"
        except (ValueError, TypeError):
            details += f"🚀 Activated: {data.get('activation_date', 'Not activated')}\n"

        # User information
        if user_info:
            details += f"\n👥 User Information\n"
            details += f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
            details += f"📝 Name: {user_info.get('name', 'Unknown')}\n"
            if 'email' in user_info:
                details += f"📧 Email: {user_info.get('email')}\n"

        # Configure tags for colored text
        self.details_text.tag_configure("red", foreground=COLORS["danger"])

        # Insert the details text
        self.details_text.insert(tk.END, details)

        # Apply red color to specific sections
        if data.get('blocked_until') or data.get('attempt_count', 0) > 0:
            # Find and tag the blocked message
            text = self.details_text.get(1.0, tk.END)

            # Tag blocked message if present
            if "BLOCKED:" in text:
                start_idx = text.find("BLOCKED:")
                if start_idx != -1:
                    line_start = "1.0 + %d chars" % start_idx
                    line_end = line_start + " lineend"
                    self.details_text.tag_add("red", line_start, line_end)

            # Tag unauthorized attempts message if present
            if "Unauthorized device attempts:" in text:
                start_idx = text.find("Unauthorized device attempts:")
                if start_idx != -1:
                    line_start = "1.0 + %d chars" % start_idx
                    line_end = line_start + " lineend"
                    self.details_text.tag_add("red", line_start, line_end)

        self.details_text.config(state=tk.DISABLED)

        # Store selected key
        self.selected_key = key

        # Update status bar
        self.status_label.config(text=f"Selected license: {key}")

        # Enable/disable buttons based on license status
        if status == "Active":
            self.activate_btn.config(state=tk.DISABLED)
            self.deactivate_btn.config(state=tk.NORMAL)
        else:
            self.activate_btn.config(state=tk.NORMAL)
            self.deactivate_btn.config(state=tk.DISABLED)

    def activate_license(self):
        if not hasattr(self, 'selected_key'):
            messagebox.showwarning("Warning", "Please select a license first")
            return

        # Ask for days to extend
        days = simpledialog.askinteger("Extend License", "Enter days to extend (leave empty for no extension):",
                                      parent=self.root, minvalue=0)

        # Reactivate license
        reactivate_license(self.selected_key, days)

        # Refresh licenses
        self.load_licenses()
        messagebox.showinfo("Success", f"License {self.selected_key} has been activated")

    def deactivate_license(self):
        if not hasattr(self, 'selected_key'):
            messagebox.showwarning("Warning", "Please select a license first")
            return

        # Confirm deactivation
        if not messagebox.askyesno("Confirm", f"Are you sure you want to deactivate license {self.selected_key}?"):
            return

        # Deactivate license
        deactivate_license(self.selected_key)

        # Refresh licenses
        self.load_licenses()
        messagebox.showinfo("Success", f"License {self.selected_key} has been deactivated")

    def delete_license(self):
        if not hasattr(self, 'selected_key'):
            messagebox.showwarning("Warning", "Please select a license first")
            return

        # Confirm deletion with a more serious warning
        if not messagebox.askyesno("Confirm Deletion",
                                  f"Are you sure you want to DELETE license {self.selected_key}?\n\nThis action cannot be undone!",
                                  icon='warning'):
            return

        # Delete license
        if delete_license(self.selected_key):
            # Refresh licenses
            self.load_licenses()
            messagebox.showinfo("Success", f"License {self.selected_key} has been deleted")

            # Clear selection
            if hasattr(self, 'selected_key'):
                delattr(self, 'selected_key')

            # Clear details text
            self.details_text.config(state=tk.NORMAL)
            self.details_text.delete(1.0, tk.END)
            self.details_text.config(state=tk.DISABLED)

            # Update status bar
            self.status_label.config(text="License deleted")
        else:
            messagebox.showerror("Error", f"Failed to delete license {self.selected_key}")

    def extend_license(self):
        if not hasattr(self, 'selected_key'):
            messagebox.showwarning("Warning", "Please select a license first")
            return

        # Ask for days to extend
        days = simpledialog.askinteger("Extend License", "Enter days to extend:",
                                      parent=self.root, minvalue=1)

        if not days:
            return

        # Get license data
        ref = db.reference(f'licenses/{self.selected_key}')
        license_data = ref.get()

        if not license_data:
            messagebox.showerror("Error", f"License key {self.selected_key} not found.")
            return

        # Calculate new expiry date
        try:
            current_expiry = datetime.fromisoformat(license_data.get('expiry_date', datetime.now().isoformat()))
        except ValueError:
            current_expiry = datetime.now()

        new_expiry = (current_expiry + timedelta(days=days)).isoformat()

        # Update license
        ref.update({
            'expiry_date': new_expiry
        })

        # Refresh licenses
        self.load_licenses()
        messagebox.showinfo("Success", f"License {self.selected_key} has been extended to {new_expiry}")

    def edit_user_info(self):
        if not hasattr(self, 'selected_key'):
            messagebox.showwarning("Warning", "Please select a license first")
            return

        # Get license data
        ref = db.reference(f'licenses/{self.selected_key}')
        license_data = ref.get()

        if not license_data:
            messagebox.showerror("Error", f"License key {self.selected_key} not found.")
            return

        # Get current user info
        user_info = license_data.get('user_info', {})

        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Edit User Information")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        dialog.grab_set()

        # Set icon for dialog
        set_window_icon(dialog)

        # Create form
        ttk.Label(dialog, text="User Name:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        name_var = tk.StringVar(value=user_info.get('name', ''))
        ttk.Entry(dialog, textvariable=name_var, width=20).grid(row=0, column=1, sticky=tk.W, pady=5, padx=5)

        ttk.Label(dialog, text="User Email:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
        email_var = tk.StringVar(value=user_info.get('email', ''))
        ttk.Entry(dialog, textvariable=email_var, width=20).grid(row=1, column=1, sticky=tk.W, pady=5, padx=5)

        # Save function
        def save_user_info():
            new_user_info = {
                'name': name_var.get(),
                'email': email_var.get()
            }

            # Update user info
            if update_user_info(self.selected_key, new_user_info):
                messagebox.showinfo("Success", "User information updated")
                dialog.destroy()
                self.load_licenses()
            else:
                messagebox.showerror("Error", "Failed to update user information")

        # Save button
        ttk.Button(dialog, text="Save", command=save_user_info).grid(row=2, column=1, pady=10, padx=5)

        # Cancel button
        ttk.Button(dialog, text="Cancel", command=dialog.destroy).grid(row=2, column=0, pady=10, padx=5)

    def generate_license(self):
        # Get form values
        try:
            days = int(self.days_var.get())
            if days <= 0:
                messagebox.showerror("Error", "Days must be a positive number")
                return
        except ValueError:
            messagebox.showerror("Error", "Days must be a number")
            return

        role = self.role_var.get()
        license_type = self.type_var.get()
        user_name = self.user_name_var.get()
        user_email = self.user_email_var.get()

        # Create user info
        user_info = {}
        if user_name:
            user_info['name'] = user_name
        if user_email:
            user_info['email'] = user_email

        # Generate license
        license_key = generate_license(days, role, license_type, user_info if user_info else None)

        # Show result
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"Generated license key: {license_key}\n")
        self.result_text.insert(tk.END, f"Expires after: {days} days\n")
        self.result_text.insert(tk.END, f"Role: {role}\n")
        self.result_text.insert(tk.END, f"Type: {license_type}\n")
        if user_info:
            self.result_text.insert(tk.END, f"User: {user_info.get('name', '')}\n")
            self.result_text.insert(tk.END, f"Email: {user_info.get('email', '')}\n")

        # Refresh licenses
        self.load_licenses()

    def create_3day_license(self):
        # Set form values for 3-day license (now permanent type instead of temporary)
        self.days_var.set("3")
        self.role_var.set("user")
        self.type_var.set("permanent")  # Changed from "temporary" to "permanent" to remove one-time limitation

        # Generate license
        self.generate_license()

# Import the center_window function from main.py
# This is now imported at the top of the file
# from main import center_window

def center_window(window, width, height):
    """Center a window on the screen"""
    # Get screen width and height
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    # Calculate position
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    # Set window position
    window.geometry(f"{width}x{height}+{x}+{y}")

def launch_gui():
    """Launch the graphical user interface"""
    root = tk.Tk()

    # Set the icon for the main window and all message boxes
    try:
        # This sets the default icon for all windows including message boxes
        root.iconbitmap(default="LogoAdmin.ico")
    except tk.TclError:
        print("Warning: Could not load LogoAdmin.ico as default icon")

    # Center the window on the screen
    center_window(root, 1200, 800)

    # Create the application instance (no need to store it in a variable)
    LicenseManagerGUI(root)
    root.mainloop()

def main():
    parser = argparse.ArgumentParser(description='License Management Tool')
    subparsers = parser.add_subparsers(dest='command')

    # Generate command
    generate_parser = subparsers.add_parser('generate', help='Generate a new license key')
    generate_parser.add_argument('days', type=int, help='Number of days license will be valid')
    generate_parser.add_argument('--role', choices=['user', 'admin'], default='user',
                               help='Role for this license (user or admin)')
    generate_parser.add_argument('--type', choices=['permanent', 'temporary'], default='permanent',
                               help='Type of license (permanent or temporary)')
    generate_parser.add_argument('--user-name', help='Name of the user')
    generate_parser.add_argument('--user-email', help='Email of the user')

    # List command
    subparsers.add_parser('list', help='List all licenses')

    # Deactivate command
    deactivate_parser = subparsers.add_parser('deactivate', help='Deactivate a license')
    deactivate_parser.add_argument('license_key', help='License key to deactivate')

    # Reactivate command
    reactivate_parser = subparsers.add_parser('reactivate', help='Reactivate a license')
    reactivate_parser.add_argument('license_key', help='License key to reactivate')
    reactivate_parser.add_argument('--days', type=int, help='Extend validity by this many days')

    # GUI command
    subparsers.add_parser('gui', help='Launch the graphical user interface')

    args = parser.parse_args()

    if args.command == 'generate':
        user_info = {}
        if args.user_name:
            user_info['name'] = args.user_name
        if args.user_email:
            user_info['email'] = args.user_email

        generate_license(args.days, args.role, args.type, user_info if user_info else None)
    elif args.command == 'list':
        list_licenses()
    elif args.command == 'deactivate':
        deactivate_license(args.license_key)
    elif args.command == 'reactivate':
        reactivate_license(args.license_key, args.days)
    elif args.command == 'gui':
        launch_gui()
    else:
        parser.print_help()

if __name__ == '__main__':
    # Launch GUI directly instead of using command line
    launch_gui()